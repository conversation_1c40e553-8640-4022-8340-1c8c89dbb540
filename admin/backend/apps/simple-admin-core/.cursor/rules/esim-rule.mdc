---
description: 
globs: 
alwaysApply: true
---
---
说明: 本规则基于Vben Admin Pro的项目的最佳实践和编码标准。
globs: *.js,*.jsx,*.ts,*.tsx,*.vue
---

## 1. 代码组织和结构
### 1.1. 目录结构
- **src/api/**: API 接口定义和请求处理
  - 按功能模块分类
  - 每个模块独立文件
  - 统一使用 TypeScript 类型定义
- **src/adapter/**: 组件适配器
  - `form.ts`: 表单组件适配
  - `vxe-table.ts`: 表格组件适配
  - 其他自定义适配器
- **src/components/**: 可复用组件
  - 按功能分类组织
  - 每个组件独立目录
  - 包含组件文档
- **src/enums/**: 枚举定义
  - 业务相关枚举
  - 系统配置枚举
  - 状态码枚举
- **src/views/**: 页面组件
  - 按路由结构组织
  - 每个页面独立目录
  - 包含页面相关组件
- **src/store/**: 状态管理
  - 按模块分类
  - 使用 TypeScript 类型
- **src/router/**: 路由配置
  - 路由定义
  - 权限控制
- **src/locales/**: 国际化资源
  - 按语言分类
  - 按模块组织
- **src/utils/**: 工具函数
  - 通用工具
  - 业务工具
- **src/types/**: 类型定义
  - 全局类型
  - 业务类型
- **src/layouts/**: 布局组件
  - 页面布局
  - 导航组件

### 1.2. 文件命名约定
- **组件文件**: 使用帕斯卡命名法（例如 `UserProfile.vue`）
- **工具文件**: 使用小写字母，用连字符分隔（例如 `date-utils.ts`）
- **类型文件**: 使用 `.d.ts` 后缀（例如 `user.d.ts`）
- **样式文件**: 与组件同名（例如 `UserProfile.scss`）
- **测试文件**: 使用 `.spec.ts` 或 `.test.ts` 后缀

### 1.3. 开发标准约定
- **样式规范**:
  - 使用 `tailwindcss` 作为主要样式解决方案
  - 复杂样式使用 `scss` 模块
  - 遵循 BEM 命名规范
- **列表组件**:
  - 统一使用 `Vxe Table`
  - 使用 `useVbenVxeGrid` 适配器
  - 实现分页、排序、筛选功能
- **表单组件**:
  - 使用 `VbenForm` 组件
  - 通过 `useVbenForm` 适配器使用
  - 实现表单验证和提交
- **页面结构**:
  - `index.vue`: 页面主组件
  - `schema.ts`: 页面配置和类型定义
  - `form.vue`: 表单组件（如需要）
  - `api.ts`: 页面相关 API
- **对话框组件**:
  - 使用 `useVbenModal` 管理
  - 实现统一的样式和行为
  - 支持自定义配置

### 1.4. 代码质量规范
- **TypeScript**:
  - 严格模式
  - 类型定义完整
  - 避免使用 `any`
- **代码风格**:
  - 使用 ESLint 和 Prettier
  - 遵循项目配置的规则
  - 保持代码整洁
- **注释规范**:
  - 组件顶部添加功能说明
  - 复杂逻辑添加注释
  - 使用 JSDoc 格式
- **性能优化**:
  - 合理使用缓存
  - 避免不必要的渲染
  - 优化大数据列表
