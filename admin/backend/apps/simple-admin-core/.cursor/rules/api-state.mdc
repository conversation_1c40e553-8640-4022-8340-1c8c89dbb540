---
description: 
globs: 
alwaysApply: false
---
## 1. API 调用规范
### 1.1. API 文件组织
- **目录结构**: API 文件按功能模块组织在 `src/api` 目录下
- **文件命名**: 使用小写字母，用连字符分隔，例如 `user-profile.ts`
- **接口命名**: 使用动词前缀，如 `get`、`create`、`update`、`delete`

### 1.2. API 调用规范
- **错误处理**: 所有 API 调用必须包含错误处理
- **类型定义**: 所有 API 响应必须定义 TypeScript 接口
- **请求配置**: 使用统一的请求配置和拦截器

### 1.3. 状态管理
- **Store 组织**: 按功能模块组织 store 文件
- **状态更新**: 使用 mutations 进行同步更新，使用 actions 进行异步操作
- **状态访问**: 使用 getters 访问派生状态

### 1.4. 数据缓存
- **缓存策略**: 实现适当的数据缓存策略
- **缓存失效**: 在数据更新时正确清除缓存
- **缓存时间**: 设置合理的缓存过期时间

### 1.5. 性能优化
- **请求合并**: 合并多个相关请求
- **数据预加载**: 实现必要的数据预加载
- **懒加载**: 对非关键数据进行懒加载
