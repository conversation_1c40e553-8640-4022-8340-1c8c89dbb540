---
description: 
globs: 
alwaysApply: false
---
## 1. 技术栈规范
### 1.1. 核心框架和工具
- **Vue 3**: 使用 Vue 3 作为核心框架
- **TypeScript**: 所有代码必须使用 TypeScript 编写
- **Vite**: 使用 Vite 作为构建工具
- **TailwindCSS**: 使用 TailwindCSS 进行样式管理

### 1.2. 依赖管理
- **包管理器**: 使用 pnpm 作为包管理器
- **依赖版本**: 所有依赖版本必须固定，不允许使用 ^ 或 ~ 符号
- **依赖更新**: 定期运行 `pnpm update` 检查依赖更新，但需要经过测试才能更新

### 1.3. 开发工具
- **ESLint**: 使用 ESLint 进行代码检查
- **Prettier**: 使用 Prettier 进行代码格式化
- **TypeScript**: 严格模式，启用所有类型检查选项

### 1.4. 构建和部署
- **构建命令**: 使用 `pnpm build` 进行生产环境构建
- **开发命令**: 使用 `pnpm dev` 启动开发服务器
- **环境变量**: 使用 `.env` 文件管理环境变量，区分开发和生产环境

### 1.5. 代码质量
- **类型检查**: 所有代码必须通过 TypeScript 类型检查
- **代码覆盖率**: 保持测试覆盖率在 80% 以上
- **性能优化**: 定期进行性能分析和优化
