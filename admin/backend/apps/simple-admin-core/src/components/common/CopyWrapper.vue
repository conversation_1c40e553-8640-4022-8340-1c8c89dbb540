<template>
  <div class="copy-wrapper" :class="wrapperClass">
    <!-- 点击元素复制模式 -->
    <div
      v-if="type === 'click'"
      class="copy-click-area"
      :class="clickAreaClass"
      @click="handleCopy"
    >
      <slot />
    </div>

    <!-- 图标复制模式 -->
    <div v-else class="copy-icon-container" :class="containerClass">
      <slot />
      
      <!-- 复制图标 -->
      <div
        class="copy-icon"
        :class="[iconClass, positionClass]"
        @click="handleCopy"
        :title="copyTitle"
      >
        <CopyOutlined :style="iconStyle" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, type CSSProperties } from 'vue';
import { CopyOutlined } from '@ant-design/icons-vue';
import { useClipboard } from '@vueuse/core';
import { message } from 'ant-design-vue';
import { $t } from '@vben/locales';

export interface CopyWrapperProps {
  /** 复制的内容 */
  copyText: string;
  /** 复制方式：click-点击元素复制，icon-点击图标复制 */
  type?: 'click' | 'icon';
  /** 图标位置：仅在type为icon时有效 */
  position?: 'top' | 'right' | 'bottom' | 'left';
  /** 复制成功提示文本 */
  successMessage?: string;
  /** 图标大小 */
  iconSize?: number;
  /** 图标颜色 */
  iconColor?: string;
  /** 自定义样式类名 */
  wrapperClass?: string;
  /** 点击区域样式类名 */
  clickAreaClass?: string;
  /** 容器样式类名 */
  containerClass?: string;
  /** 图标样式类名 */
  iconClass?: string;
  /** 复制图标提示文本 */
  copyTitle?: string;
}

const props = withDefaults(defineProps<CopyWrapperProps>(), {
  type: 'click',
  position: 'right',
  successMessage: '',
  iconSize: 14,
  iconColor: '#666',
  wrapperClass: '',
  clickAreaClass: '',
  containerClass: '',
  iconClass: '',
  copyTitle: '复制',
});

const { copy, isSupported } = useClipboard();

// 计算图标样式
const iconStyle = computed<CSSProperties>(() => ({
  fontSize: `${props.iconSize}px`,
  color: props.iconColor,
}));

// 计算图标位置样式类
const positionClass = computed(() => {
  const positionMap = {
    top: 'copy-icon-top',
    right: 'copy-icon-right',
    bottom: 'copy-icon-bottom',
    left: 'copy-icon-left',
  };
  return positionMap[props.position];
});

// 处理复制操作
const handleCopy = async () => {
  if (!isSupported.value) {
    message.error('当前浏览器不支持复制功能');
    return;
  }

  if (!props.copyText) {
    message.warning('没有可复制的内容');
    return;
  }

  try {
    await copy(props.copyText);
    const successMsg = props.successMessage || $t('common.successful');
    message.success(successMsg);
  } catch (error) {
    console.error('复制失败:', error);
    message.error('复制失败');
  }
};
</script>

<style scoped>
.copy-wrapper {
  display: inline-block;
}

.copy-click-area {
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.copy-click-area:hover {
  opacity: 0.8;
}

.copy-icon-container {
  position: relative;
  display: inline-block;
}

.copy-icon {
  position: absolute;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.copy-icon:hover {
  background: rgba(240, 240, 240, 1);
  border-color: #40a9ff;
  color: #40a9ff !important;
}

/* 图标位置样式 */
.copy-icon-top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 4px;
}

.copy-icon-right {
  top: 50%;
  left: 100%;
  transform: translateY(-50%);
  margin-left: 4px;
}

.copy-icon-bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 4px;
}

.copy-icon-left {
  top: 50%;
  right: 100%;
  transform: translateY(-50%);
  margin-right: 4px;
}

/* 响应式隐藏图标，鼠标悬停时显示 */
.copy-icon-container .copy-icon {
  opacity: 0;
  visibility: hidden;
}

.copy-icon-container:hover .copy-icon {
  opacity: 1;
  visibility: visible;
}
</style>
