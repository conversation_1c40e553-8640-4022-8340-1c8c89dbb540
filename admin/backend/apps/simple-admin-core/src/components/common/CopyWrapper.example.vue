<template>
  <div class="p-5 max-w-4xl">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">CopyWrapper 组件使用示例</h2>

    <!-- 示例1: 点击元素复制 -->
    <div class="mb-8 p-5 border border-gray-200 rounded-lg bg-gray-50">
      <h3 class="mt-0 mb-4 text-lg font-semibold text-blue-600">1. 点击元素复制模式</h3>
      <div class="mb-4 p-3 bg-white rounded border flex items-center gap-3">
        <CopyWrapper
          type="click"
          copy-text="这是要复制的文本内容"
          success-message="文本复制成功！"
          click-area-class="px-3 py-2 bg-gradient-to-r from-blue-500 to-green-500 rounded text-white"
        >
          <Button type="primary">点击我复制文本</Button>
        </CopyWrapper>
      </div>

      <div class="mb-4 p-3 bg-white rounded border flex items-center gap-3">
        <CopyWrapper
          type="click"
          copy-text="<EMAIL>"
          success-message="邮箱地址已复制"
        >
          <Tag color="blue"><EMAIL></Tag>
        </CopyWrapper>
      </div>
    </div>

    <!-- 示例2: 图标复制模式 - 不同位置 -->
    <div class="mb-8 p-5 border border-gray-200 rounded-lg bg-gray-50">
      <h3 class="mt-0 mb-4 text-lg font-semibold text-blue-600">2. 图标复制模式 - 不同位置</h3>

      <div class="grid grid-cols-2 gap-4">
        <div class="p-3 bg-white rounded border flex items-center gap-3">
          <span class="w-16 text-sm text-gray-600">右侧:</span>
          <CopyWrapper
            type="icon"
            position="right"
            copy-text="右侧复制内容"
            :icon-size="16"
            icon-color="#1890ff"
          >
            <Button>悬停显示右侧复制图标</Button>
          </CopyWrapper>
        </div>

        <div class="p-3 bg-white rounded border flex items-center gap-3">
          <span class="w-16 text-sm text-gray-600">左侧:</span>
          <CopyWrapper
            type="icon"
            position="left"
            copy-text="左侧复制内容"
            :icon-size="14"
            icon-color="#52c41a"
          >
            <Button>悬停显示左侧复制图标</Button>
          </CopyWrapper>
        </div>

        <div class="p-3 bg-white rounded border flex items-center gap-3">
          <span class="w-16 text-sm text-gray-600">上方:</span>
          <CopyWrapper
            type="icon"
            position="top"
            copy-text="上方复制内容"
            :icon-size="12"
            icon-color="#fa8c16"
          >
            <Button>悬停显示上方复制图标</Button>
          </CopyWrapper>
        </div>

        <div class="p-3 bg-white rounded border flex items-center gap-3">
          <span class="w-16 text-sm text-gray-600">下方:</span>
          <CopyWrapper
            type="icon"
            position="bottom"
            copy-text="下方复制内容"
            :icon-size="18"
            icon-color="#eb2f96"
          >
            <Button>悬停显示下方复制图标</Button>
          </CopyWrapper>
        </div>
      </div>
    </div>

    <!-- 示例3: 实际应用场景 -->
    <div class="mb-8 p-5 border border-gray-200 rounded-lg bg-gray-50">
      <h3 class="mt-0 mb-4 text-lg font-semibold text-blue-600">3. 实际应用场景</h3>

      <div class="space-y-4">
        <!-- 设备编号复制 -->
        <div class="p-3 bg-white rounded border flex items-center gap-3">
          <span class="w-24 text-sm text-gray-600">设备编号:</span>
          <CopyWrapper
            type="icon"
            position="right"
            copy-text="DEV-2024-001"
            success-message="设备编号已复制"
            copy-title="复制设备编号"
          >
            <Button type="link" style="color: green;">DEV-2024-001</Button>
          </CopyWrapper>
        </div>

        <!-- API Token 复制 -->
        <div class="p-3 bg-white rounded border flex items-center gap-3">
          <span class="w-24 text-sm text-gray-600">API Token:</span>
          <CopyWrapper
            type="click"
            copy-text="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            success-message="Token已复制到剪贴板"
          >
            <Button type="primary" size="small">复制Token</Button>
          </CopyWrapper>
        </div>

        <!-- 链接复制 -->
        <div class="p-3 bg-white rounded border flex items-center gap-3">
          <span class="w-24 text-sm text-gray-600">分享链接:</span>
          <CopyWrapper
            type="icon"
            position="right"
            copy-text="https://example.com/share/123456"
            success-message="链接已复制"
            :icon-size="16"
            icon-color="#1890ff"
          >
            <a href="https://example.com/share/123456" target="_blank" class="text-blue-500 underline">
              https://example.com/share/123456
            </a>
          </CopyWrapper>
        </div>
      </div>
    </div>

    <!-- 示例4: 自定义样式 -->
    <div class="mb-8 p-5 border border-gray-200 rounded-lg bg-gray-50">
      <h3 class="mt-0 mb-4 text-lg font-semibold text-blue-600">4. 自定义样式</h3>

      <div class="space-y-4">
        <div class="p-3 bg-white rounded border flex items-center gap-3">
          <CopyWrapper
            type="click"
            copy-text="自定义样式的复制内容"
            wrapper-class="border-2 border-dashed border-blue-500 rounded"
            click-area-class="px-3 py-2 bg-gradient-to-r from-blue-500 to-green-500 rounded text-white"
          >
            <div class="px-4 py-2 bg-gray-100 rounded border-2 border-dashed border-gray-300 cursor-pointer transition-colors hover:bg-blue-50 hover:border-blue-500">
              <span>自定义样式的可复制区域</span>
            </div>
          </CopyWrapper>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Button, Tag } from 'ant-design-vue';
import { CopyWrapper } from './index';

defineOptions({
  name: 'CopyWrapperExample',
});
</script>


