<template>
  <div class="copy-wrapper-examples">
    <h2>CopyWrapper 组件使用示例</h2>
    
    <!-- 示例1: 点击元素复制 -->
    <div class="example-section">
      <h3>1. 点击元素复制模式</h3>
      <div class="example-item">
        <CopyWrapper
          type="click"
          copy-text="这是要复制的文本内容"
          success-message="文本复制成功！"
          click-area-class="custom-click-area"
        >
          <Button type="primary">点击我复制文本</Button>
        </CopyWrapper>
      </div>
      
      <div class="example-item">
        <CopyWrapper
          type="click"
          copy-text="<EMAIL>"
          success-message="邮箱地址已复制"
        >
          <Tag color="blue"><EMAIL></Tag>
        </CopyWrapper>
      </div>
    </div>

    <!-- 示例2: 图标复制模式 - 不同位置 -->
    <div class="example-section">
      <h3>2. 图标复制模式 - 不同位置</h3>
      
      <div class="example-item">
        <span>右侧图标: </span>
        <CopyWrapper
          type="icon"
          position="right"
          copy-text="右侧复制内容"
          :icon-size="16"
          icon-color="#1890ff"
        >
          <Button>悬停显示右侧复制图标</Button>
        </CopyWrapper>
      </div>

      <div class="example-item">
        <span>左侧图标: </span>
        <CopyWrapper
          type="icon"
          position="left"
          copy-text="左侧复制内容"
          :icon-size="14"
          icon-color="#52c41a"
        >
          <Button>悬停显示左侧复制图标</Button>
        </CopyWrapper>
      </div>

      <div class="example-item">
        <span>上方图标: </span>
        <CopyWrapper
          type="icon"
          position="top"
          copy-text="上方复制内容"
          :icon-size="12"
          icon-color="#fa8c16"
        >
          <Button>悬停显示上方复制图标</Button>
        </CopyWrapper>
      </div>

      <div class="example-item">
        <span>下方图标: </span>
        <CopyWrapper
          type="icon"
          position="bottom"
          copy-text="下方复制内容"
          :icon-size="18"
          icon-color="#eb2f96"
        >
          <Button>悬停显示下方复制图标</Button>
        </CopyWrapper>
      </div>
    </div>

    <!-- 示例3: 实际应用场景 -->
    <div class="example-section">
      <h3>3. 实际应用场景</h3>
      
      <!-- 设备编号复制 -->
      <div class="example-item">
        <span>设备编号: </span>
        <CopyWrapper
          type="icon"
          position="right"
          copy-text="DEV-2024-001"
          success-message="设备编号已复制"
          copy-title="复制设备编号"
        >
          <Button type="link" style="color: green;">DEV-2024-001</Button>
        </CopyWrapper>
      </div>

      <!-- API Token 复制 -->
      <div class="example-item">
        <span>API Token: </span>
        <CopyWrapper
          type="click"
          copy-text="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
          success-message="Token已复制到剪贴板"
        >
          <Button type="primary" size="small">复制Token</Button>
        </CopyWrapper>
      </div>

      <!-- 链接复制 -->
      <div class="example-item">
        <span>分享链接: </span>
        <CopyWrapper
          type="icon"
          position="right"
          copy-text="https://example.com/share/123456"
          success-message="链接已复制"
          :icon-size="16"
          icon-color="#1890ff"
        >
          <a href="https://example.com/share/123456" target="_blank">
            https://example.com/share/123456
          </a>
        </CopyWrapper>
      </div>
    </div>

    <!-- 示例4: 自定义样式 -->
    <div class="example-section">
      <h3>4. 自定义样式</h3>
      
      <div class="example-item">
        <CopyWrapper
          type="click"
          copy-text="自定义样式的复制内容"
          wrapper-class="custom-wrapper"
          click-area-class="custom-click-area"
        >
          <div class="custom-content">
            <span>自定义样式的可复制区域</span>
          </div>
        </CopyWrapper>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Button, Tag } from 'ant-design-vue';
import { CopyWrapper } from './index';

defineOptions({
  name: 'CopyWrapperExample',
});
</script>

<style scoped>
.copy-wrapper-examples {
  padding: 20px;
  max-width: 800px;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
}

.example-section h3 {
  margin-top: 0;
  color: #1890ff;
}

.example-item {
  margin-bottom: 15px;
  padding: 10px;
  background: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.custom-wrapper {
  border: 2px dashed #1890ff;
  border-radius: 4px;
}

.custom-click-area {
  padding: 8px 12px;
  background: linear-gradient(45deg, #1890ff, #52c41a);
  border-radius: 4px;
  color: white;
}

.custom-content {
  padding: 10px 15px;
  background: #f0f2f5;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}

.custom-content:hover {
  background: #e6f7ff;
}
</style>
