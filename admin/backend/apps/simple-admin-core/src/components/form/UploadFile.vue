<script lang="ts" setup>
import type { UploadProps } from 'ant-design-vue';
import { InboxOutlined } from '@ant-design/icons-vue';
import { message, UploadDragger } from 'ant-design-vue';
import { ref, watch, computed, type PropType } from 'vue';
import { uploadFile } from '#/api/fms/file';

defineOptions({
  name: 'UploadFile',
});

const props = defineProps({
  value: {
    type: Object as PropType<{ url: string, name: string }>,
    default: () => ({ url: '', name: '' }),
  },
  maxCount: {
    type: Number,
    default: 1,
  },
  maxSize: {
    type: Number,
    default: 10 * 1024 * 1024,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  accept: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});

const emits = defineEmits(['update:value']);

const fileList = ref<UploadProps['fileList']>([]);

// 构建 accept 字符串
const acceptString = computed(() => {
  return props.accept.map(ext => `.${ext}`).join(',');
});

async function handleUpload(options: any) {
  const { file, onProgress, onSuccess, onError } = options;
  
  if (!onProgress || !onSuccess || !onError) {
    console.log("不包含onProgress,onSuccess,onError");
    return;
  }

  try {
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      onProgress({ percent: Math.random() * 100 });
    }, 100);

    const result = await uploadFile(file);
    
    clearInterval(progressInterval);
    
    if (result.code === 0) {
      emits('update:value', { url: result.data.url, name: result.data.name });
      message.success('上传成功');
      onSuccess(result);
    } else {
      message.error('上传失败');
      onError(new Error(result.message || '上传失败'));
    }
  } catch (error) {
    message.error('上传失败');
    onError(error instanceof Error ? error : new Error('上传失败'));
  }
}

// 处理文件删除
function handleRemove(file: any) {
  const index = fileList.value?.findIndex(item => item.uid === file.uid);
  if (index !== undefined && index > -1) {
    emits('update:value', { url: '', name: '' });
  }
}

// 当值被外部清空时，清空文件列表
watch(() => props.value, (newVal) => {
  if (!newVal?.url) {
    fileList.value = [];
  }
});
</script>

<template>
  <UploadDragger
    v-model:file-list="fileList"
    :custom-request="handleUpload"
    :multiple="props.multiple"
    :max-count="props.maxCount"
    :max-size="props.maxSize"
    :accept="acceptString"
    :show-upload-list="{ showDownloadIcon: true, showRemoveIcon: true }"
    @remove="handleRemove"
  >
    <p class="ant-upload-drag-icon">
      <InboxOutlined />
    </p>
    <p class="ant-upload-text">
      点击或拖拽文件到此区域上传
    </p>
    <p class="ant-upload-hint">
      支持任意格式文件
      <template v-if="accept.length">
        ({{ accept.join(', ') }})
      </template>
    </p>
  </UploadDragger>
</template>
