import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  // {
  //   component: BasicLayout,
  //   meta: {
  //     icon: 'lucide:layout-dashboard',
  //     sort: -1,
  //     title: $t('page.dashboard.title'),
  //   },
  //   name: 'Dashboard',
  //   path: '/',
  //   children: [
  //     {
  //       name: 'Analytics',
  //       path: '/analytics',
  //       component: () => import('#/views/dashboard/analytics/index.vue'),
  //       meta: {
  //         affixTab: true,
  //         icon: 'lucide:area-chart',
  //         title: $t('page.dashboard.analytics'),
  //       },
  //     },
  //     {
  //       name: 'Workspace',
  //       path: '/dashboard',
  //       component: () => import('#/views/dashboard/workbench/index.vue'),
  //       meta: {
  //         icon: 'carbon:workspace',
  //         title: $t('page.dashboard.workspace'),
  //       },
  //     },
  //   ],
  // },
];

export default routes;
