import type { VxeGridProps } from '#/adapter/vxe-table';
import { h } from 'vue';
import { type VbenFormProps } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

export const tableColumns: VxeGridProps = {
  columns: [
    {
      title: '厂商名称',
      field: 'name',
      width: 150,
    },
    {
      title: '联系人',
      field: 'contactPerson',
      width: 100,
    },
    {
      title: '联系电话',
      field: 'contactPhone',
      width: 120,
    },
    {
      title: '邮箱',
      field: 'contactEmail',
      width: 150,
    },
    {
      title: '地址',
      field: 'address',
    },
    {
      title: '开户行',
      field: 'bankName',
      width: 150,
    },
    {
      title: '银行账号',
      field: 'bankAccount',
      width: 180,
    },
    {
      title: '创建时间',
      field: 'createdAt',
      width: 180,
      slots: {
        default: ({ row }) => dayjs(row.createdAt * 1000).format('YYYY-MM-DD HH:mm:ss'),
      },
    },
    {
      field: 'action',
      fixed: 'right',
      slots: { default: 'action' },
      title: '操作',
      width: 120,
    },
  ],
};

export const vendorFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'name',
      label: '厂商名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入厂商名称',
      },
      rules: 'required',
    },
    {
      fieldName: 'contactPerson',
      label: '联系人',
      component: 'Input',
      componentProps: {
        placeholder: '请输入联系人姓名',
      },
    },
    {
      fieldName: 'contactPhone',
      label: '联系电话',
      component: 'Input',
      componentProps: {
        placeholder: '请输入联系电话',
      },
    },
    {
      fieldName: 'contactEmail',
      label: '邮箱',
      component: 'Input',
      componentProps: {
        placeholder: '请输入邮箱地址',
      },
    },
    {
      fieldName: 'address',
      label: '地址',
      component: 'Input',
      componentProps: {
        placeholder: '请输入地址',
      },
    },
    {
      fieldName: 'bankName',
      label: '开户行',
      component: 'Input',
      componentProps: {
        placeholder: '请输入开户行',
      },
    },
    {
      fieldName: 'bankAccount',
      label: '银行账号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入银行账号',
      },
    },
  ],
};

export const searchFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'name',
      label: '厂商名称',
      component: 'Input',
    },
    {
      fieldName: 'contactPerson',
      label: '联系人',
      component: 'Input',
    },
    {
      fieldName: 'contactPhone',
      label: '联系电话',
      component: 'Input',
    },
  ],
}; 
