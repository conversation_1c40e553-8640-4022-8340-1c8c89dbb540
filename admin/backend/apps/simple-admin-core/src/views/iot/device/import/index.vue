<template>
  <Page auto-content-height>
    <ImportModalComponent />
    <Grid>
      <template #toolbar-tools>
        <Button type="primary" @click="handleImport">
          <template #icon><ImportOutlined /></template>
          导入设备
        </Button>
      </template>
    </Grid>
  </Page>
</template>

<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import { h, ref } from 'vue';
import { Page, useVbenModal, type VbenFormProps } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { Button } from 'ant-design-vue';
import { ImportOutlined } from '@ant-design/icons-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import ImportModal from './ImportModal.vue';
import { searchFormSchemas, tableColumns } from './schemas';
import { getTaskList } from '#/api/iot/task';
import type { TaskListInfo } from '#/api/iot/model/taskModel';

defineOptions({
  name: 'DeviceImport',
});

const [ImportModalComponent, importModalApi] = useVbenModal({
  connectedComponent: ImportModal,
  onBeforeClose: () => {
    gridApi.reload();
    return true;
  },
});

const formOptions: VbenFormProps = {
  schema: [...(searchFormSchemas.schema as any)],
  collapsed: false,
  showCollapseButton: true,
  submitOnEnter: false,
};

const gridOptions: VxeGridProps<TaskListInfo> = {
  columns: tableColumns.columns,
  height: 'auto',
  keepSource: true,
  stripe: true,
  toolbarConfig: {
    custom: true,
    export: false,
    import: false,
    refresh: true,
    zoom: true,
  },
  pagerConfig: {},
  importConfig: {
    types: ['xlsx', 'xls'],
    mode: 'cell',
    remote: true,
    params: {
      type: 3, // 设备导入类型为2
    },
    importMethod: async ({ $table, $grid, file, options }) => {
      console.log($table, $grid, file, options);
      return true;
    },
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        const res = await getTaskList({
          page: {
            page: page.currentPage,
            pageSize: page.pageSize,
          },
          type: 3, // 设备导入类型为2
        });
        return res.data;
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});

function handleImport() {
  importModalApi.setData({
    gridApi,
  });
  importModalApi.open();
}

function handleImportSuccess() {
  gridApi.reload();
  importModalApi.close();
}
</script>
