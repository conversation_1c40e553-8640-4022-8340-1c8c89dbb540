# 设备导入功能说明

## 导入文件格式要求

### 支持的文件格式
- Excel文件：`.xlsx`, `.xls`
- CSV文件：`.csv`
- 文件大小限制：10MB

### Excel模板字段说明

导入的Excel文件应包含以下列（列名必须与下表中的字段名完全一致）：

| 字段名 | 中文名称 | 是否必填 | 数据类型 | 说明 |
|--------|----------|----------|----------|------|
| device_no | 设备号 | 是 | 字符串(50) | 设备的唯一标识，不能重复 |
| box_no | 箱号 | 否 | 字符串(50) | 设备包装箱号 |
| card_board_no | 卡板号 | 否 | 字符串(50) | 设备卡板编号 |
| charger_sn | 充电器序列号 | 否 | 字符串(50) | 主充电器序列号 |
| charger_sn2 | 备用充电器序列号 | 否 | 字符串(50) | 备用充电器序列号 |
| random_code | 随机码 | 否 | 字符串(50) | 设备随机码 |
| quantity | 数量 | 否 | 整数 | 设备数量 |
| package_date | 包装时间 | 否 | 日期时间 | 格式：YYYY-MM-DD HH:mm:ss |
| imei | IMEI号 | 否 | 字符串(50) | 设备IMEI号 |
| imsi | IMSI号 | 否 | 字符串(50) | 设备IMSI号 |
| mac | MAC地址 | 否 | 字符串(50) | 设备MAC地址 |
| msisdn | 手机号 | 否 | 字符串(50) | 设备手机号 |
| ccid | CCID | 否 | 字符串(50) | 设备的CCID |
| access_number | 接入号 | 否 | 字符串(50) | 设备接入号 |
| sn | 序列号 | 否 | 字符串(50) | 设备序列号 |
| ssid | WiFi名称 | 否 | 字符串(50) | 设备无线网络名称 |
| wifi_key | WiFi密码 | 否 | 字符串(50) | 设备无线网络密码 |
| wifi_mac | 2.4G WiFi MAC | 否 | 字符串(50) | 设备2.4G无线MAC地址 |
| wifi_mac_5g | 5G WiFi MAC | 否 | 字符串(50) | 设备5G无线MAC地址 |
| speed_up_link | 上行控速 | 否 | 整数 | 上行控速，0表示不限速，单位byte |
| speed_down_link | 下行控速 | 否 | 整数 | 下行控速，0表示不限速，单位byte |
| hidden | WiFi状态 | 否 | 整数 | 0=关闭，1=打开，2=隐藏SSID |

### 导入示例

Excel文件第一行应为字段名（表头），从第二行开始为数据：

```
device_no | box_no | card_board_no | imei | imsi | mac | ...
DEV001    | BOX001 | CB001         | 123456789012345 | *************** | AA:BB:CC:DD:EE:FF | ...
DEV002    | BOX001 | CB001         | 123456789012346 | *************** | AA:BB:CC:DD:EE:FG | ...
```

### 注意事项

1. **设备号唯一性**：`device_no`字段必须唯一，如果导入的设备号已存在，可以选择是否覆盖
2. **数据格式**：请确保数据格式正确，特别是日期时间格式
3. **字段长度**：请注意各字段的长度限制，超长数据会被截断
4. **标签设置**：可以在导入时统一设置标签，用于设备分类管理
5. **厂商选择**：需要先选择设备厂商，确保厂商信息已在系统中存在

### 导入流程

1. 准备符合格式要求的Excel文件
2. 在导入页面点击"导入设备"按钮
3. 填写导入表单：
   - 选择设备厂商
   - 选择设备类型
   - 输入设备型号
   - 设置成本单价（可选）
   - 添加设备标签（可选）
   - 选择是否覆盖已存在设备
   - 上传导入文件
4. 点击确认开始导入
5. 在任务列表中查看导入进度和结果

### 错误处理

- 导入过程中如果遇到错误，可以在任务详情中查看具体的错误信息
- 支持部分成功导入，失败的记录会单独标记
- 可以下载错误报告，修正数据后重新导入
