<template>
  <Page auto-content-height>
    <VendorModalComponent />
    <Grid>
      <template #toolbar-tools>
        <Button type="primary" @click="handleAdd">
          <template #icon><PlusOutlined /></template>
          新增厂商
        </Button>
      </template>
      <!-- 自定义action -->
      <template #action="{ row }">
        <Dropdown>
          <template #overlay>
            <Menu>
              <MenuItem key="1" @click="handleEdit(row)">
                <template #icon><EditOutlined /></template>
                编辑
              </MenuItem>
              <MenuItem key="3">
                <template #icon><FundOutlined /></template>
                流水
              </MenuItem>
              <MenuItem key="2">
                <template #icon><DeleteOutlined /></template>
                删除
              </MenuItem>
            </Menu>
          </template>
          <a class="ant-dropdown-link" @click.prevent>
            <EllipsisOutlined />
          </a>
        </Dropdown>
      </template>
    </Grid>
  </Page>
</template>

<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import { onMounted, ref } from 'vue';
import { Page, useVbenModal, type VbenFormProps } from '@vben/common-ui';
import { Button,Dropdown,Menu,MenuItem } from 'ant-design-vue';
import { PlusOutlined,EllipsisOutlined,EditOutlined,DeleteOutlined,FundOutlined } from '@ant-design/icons-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import VendorModal from './VendorModal.vue';
import { searchFormSchemas, tableColumns } from './schemas';
import { getManufacturerList } from '#/api/iot/device';

defineOptions({
  name: 'VendorManagement',
});

const [VendorModalComponent, vendorModalApi] = useVbenModal({
  connectedComponent: VendorModal,
  onBeforeClose: () => {
    gridApi.reload();
    return true;
  },
});

const formOptions: VbenFormProps = {
  collapsed: false,
  schema: [...(searchFormSchemas.schema as any)],
  showCollapseButton: true,
  submitOnEnter: false,
};

const gridOptions: VxeGridProps = {
  columns: tableColumns.columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  sortConfig: {
    multiple: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const res = await getManufacturerList({
          page: {
            page: page.currentPage,
            pageSize: page.pageSize,
          },
          ...formValues,
        });
        return res.data;
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});

function handleAdd() {
  vendorModalApi.setData({
    type: 'add',
  });
  vendorModalApi.open();
}

function handleEdit(row: any) {
  vendorModalApi.setData({
    type: 'edit',
    record: row,
  });
  vendorModalApi.open();
}

function handleSuccess() {
  gridApi.reload();
  vendorModalApi.close();
}
</script> 
