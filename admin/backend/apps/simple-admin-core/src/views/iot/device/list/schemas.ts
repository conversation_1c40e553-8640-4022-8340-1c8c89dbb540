import type { VxeGridProps } from '#/adapter/vxe-table';
import { h } from 'vue';
import { type VbenFormProps } from '@vben/common-ui';
import { Button, Tag, Tooltip } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { DeviceInfo } from '#/api/iot/model/deviceModel';
import { router } from '#/router';
import { getManufacturerList } from '#/api/iot/device';

export const tableColumns: VxeGridProps<DeviceInfo> = {
  columns: [
    {
      title: '设备号',
      field: 'deviceNo',
      width: 150,
      fixed: 'left',
    },
    {
      title: 'IMEI',
      field: 'imei',
      width: 150,
    },
    {
      title: 'IMSI',
      field: 'imsi',
      width: 150,
    },
    {
      title: '手机号',
      field: 'msisdn',
      width: 130,
    },
    {
      title: 'MAC地址',
      field: 'mac',
      width: 140,
    },
    {
      title: '序列号',
      field: 'sn',
      width: 120,
    },
    {
      title: '渠道',
      field: 'channel',
      width: 120,
      slots: {
        default: ({ row }) => row.channel?.name || '-',
      },
    },
    {
      title: '关联卡片',
      field: 'card',
      width: 210,
      slots: {
        default: ({ row }) =>
          row.cards && row.cards.length > 0
            ? h(
                'div',
                { class: 'flex flex-wrap gap-1' },
                row.cards.map((card) =>
                  h(
                    Button,
                    {
                      type: 'link',
                      style: { color: 'green' },
                      onClick: () => {
                        router.push({
                          path: '/iot/card/list',
                          query: {
                            iccid: card.iccid
                          }
                        });
                      }
                    },
                    () => card.iccid
                  )
                )
              )
            : h(
                Tag,
                { color: 'red' },
                () => '暂未关联卡片'
              ),
      },
    },
    // 导入标签（标签颜色根据文字变化）
    {
      title: '导入标签',
      field: 'tags',
      width: 200,
      slots: {
        default: ({ row }) => {
          try {
            const tags = row.tags || [];

            // 生成标签颜色的函数
            const getHashColor = (str: string) => {
              let hash = 0;
              for (let i = 0; i < str.length; i++) {
                hash = str.charCodeAt(i) + ((hash << 5) - hash);
              }
              const hue = Math.abs(hash % 360);
              return `hsl(${hue}, 70%, 50%)`;
            };

            return h(
              'div',
              {
                class: 'flex flex-wrap items-center justify-center gap-1',
              },
              [
                ...tags.slice(0, 3).map((tag: any) =>
                  h(
                    Tag,
                    { color: getHashColor(tag) },
                    () => tag,
                  ),
                ),
                tags.length > 3 &&
                h(
                  Tooltip,
                  {
                    title: h(
                      'div',
                      { class: 'flex flex-wrap gap-1' },
                      tags.slice(3).map((tag: any) =>
                        h(
                          Tag,
                          { color: getHashColor(tag) },
                          () => tag,
                        ),
                      ),
                    ),
                  },
                  () =>
                    h(
                      Tag,
                      { color: 'blue' },
                      () => `+${tags.length - 3}`,
                    ),
                ),
              ],
            );
          } catch (error) {
            return '数据解析错误';
          }
        },
      },
    },
    {
      title: 'WIFI状态',
      field: 'hidden',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(
            Tag,
            {
              color:
                row.hidden === 0
                  ? 'red'
                  : row.hidden === 1
                    ? 'green'
                    : 'orange',
            },
            () => {
              switch (row.hidden) {
                case 0:
                  return '关闭';
                case 1:
                  return '打开';
                case 2:
                  return '隐藏SSID';
                default:
                  return '未知';
              }
            },
          ),
      },
    },
    {
      title: '成本价格',
      field: 'costPrice',
      width: 100,
      formatter: ({ cellValue }) => {
        return cellValue ? `¥${cellValue.toFixed(2)}` : '-';
      },
    },
    {
      title: '打包时间',
      field: 'packageDate',
      width: 180,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue * 1000).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      title: '创建时间',
      field: 'createdAt',
      width: 180,
      formatter: ({ cellValue }) => {
        return dayjs(cellValue * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
  ],
};

export const searchFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'deviceNo',
      label: '设备号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入设备号',
      },
    },
    {
      fieldName: 'imei',
      label: 'IMEI',
      component: 'Input',
      componentProps: {
        placeholder: '请输入IMEI',
      },
    },
    {
      fieldName: 'iccid',
      label: '卡号(ICCID)',
      component: 'Input',
      componentProps: {
        placeholder: '请输入关联卡片的ICCID',
      },
    },
    {
      fieldName: 'tags',
      label: '标签',
      component: 'Select',
      componentProps: {
        mode: 'tags',
        placeholder: '请输入标签，可多选',
        tokenSeparators: [',', '，', ' '],
      },
      help: '输入标签后按回车或逗号分隔，可添加多个标签',
    },
    {
      fieldName: 'channelId',
      label: '渠道',
      component: 'ApiSelect',
      componentProps: {
        api: getManufacturerList,
        params: {
          page: {
            page: 1,
            pageSize: 1000,
          }
        },
        resultField: 'data.data',
        labelField: 'name',
        valueField: 'id',
        multiple: false,
      },
    },
    {
      fieldName: 'createdAtRange',
      label: '创建时间',
      component: 'RangePicker',
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
      },
    },
    {
      fieldName: 'packageDateRange',
      label: '打包时间',
      component: 'RangePicker',
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
      },
    },
  ],
};
