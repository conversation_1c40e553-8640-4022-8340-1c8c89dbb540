<template>
  <Page auto-content-height>
    <Grid>
    </Grid>
  </Page>
</template>

<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import { h, ref } from 'vue';
import { Page, type VbenFormProps } from '@vben/common-ui';
import { Button } from 'ant-design-vue';
import { ReloadOutlined } from '@ant-design/icons-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { searchFormSchemas, tableColumns } from './schemas';
import { getDeviceList } from '#/api/iot/device';
import type { DeviceInfo } from '#/api/iot/model/deviceModel';
import dayjs from 'dayjs';

defineOptions({
  name: 'DeviceList',
});

const formOptions: VbenFormProps = {
  schema: [...(searchFormSchemas.schema as any)],
  collapsed: false,
  showCollapseButton: true,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
};

const gridOptions: VxeGridProps<DeviceInfo> = {
  columns: tableColumns.columns,
  height: 'auto',
  keepSource: true,
  stripe: true,
  toolbarConfig: {
    custom: true,
    export: false,
    import: false,
    refresh: true,
    zoom: true,
  },
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formData) => {
        // 处理时间范围参数
        const params: any = {
          page: {
            page: page.currentPage,
            pageSize: page.pageSize,
          },
          ...formData,
        };

        // 处理创建时间范围
        if (formData.createdAtRange && formData.createdAtRange.length === 2) {
          params.createdAtStart = dayjs(formData.createdAtRange[0]).unix();
          params.createdAtEnd = dayjs(formData.createdAtRange[1]).unix();
          delete params.createdAtRange;
        }

        // 处理打包时间范围
        if (formData.packageDateRange && formData.packageDateRange.length === 2) {
          params.packageDateStart = dayjs(formData.packageDateRange[0]).unix();
          params.packageDateEnd = dayjs(formData.packageDateRange[1]).unix();
          delete params.packageDateRange;
        }

        const res = await getDeviceList(params);
        return res.data;
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});
</script>
