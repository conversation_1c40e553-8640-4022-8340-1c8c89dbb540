<template>
  <Modal
    :title="modalTitle"
  >
    <Form />
  </Modal>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { message } from 'ant-design-vue';
import { vendorFormSchemas } from './schemas';
import { createManufacturer } from '#/api/iot/device';
import type { ManufacturerCreateReq } from '#/api/iot/model/deviceModel';

defineOptions({
  name: 'VendorModal',
});

const modalData = ref<{
  type: 'add' | 'edit';
  record?: any;
}>({
  type: 'add',
});

const modalTitle = computed(() => modalData.value.type === 'add' ? '新增厂商' : '编辑厂商');

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  wrapperClass: 'grid-cols-1 md:grid-cols-2 gap-4',
  schema: [...(vendorFormSchemas.schema as any)],
  showDefaultActions: false,
  commonConfig: {
    componentProps: {
      class: 'col-span-2 md:col-span-1 w-full',
    },
    labelClass: 'w-4/6',
  }
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    const validationResult = await formApi.validate();
    if (validationResult.valid) {
      try {
        const formData = await formApi.getValues();
        
        // 转换表单数据为 API 所需格式
        const apiData: ManufacturerCreateReq = {
          name: formData.name,
          contactPerson: formData.contactPerson,
          contactPhone: formData.contactPhone,
          contactEmail: formData.email,
          address: formData.address,
          bankAccount: formData.bankAccount,
          bankName: formData.bankName,
          id: modalData.value.record?.id,
        };

        const res = await createManufacturer(apiData);

        if(res.code === 0){
          message.success(modalData.value.type === 'add' ? '新增成功' : '编辑成功');
        }else{
          message.error(res.msg);
        }
        modalApi.close();
      } catch (error) {
        console.error('提交失败:', error);
        message.error('操作失败');
      }
    }
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData();
      modalData.value = {
        type: data.type,
        record: data.record,
      };
      if (data.type === 'edit' && data.record) {
        formApi.setValues(data.record);
      }
    }
  },
});

defineExpose(modalApi);
</script> 
