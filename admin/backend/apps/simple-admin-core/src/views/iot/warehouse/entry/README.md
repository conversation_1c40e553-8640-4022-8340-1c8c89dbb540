# 云仓入库管理功能

## 功能概述

云仓入库管理功能是基于 Vue3 + TypeScript + Vben Admin 架构实现的完整入库单管理系统，支持入库单的创建、编辑、确认、取消、批量录入明细等操作。

## 文件结构

```
src/views/iot/warehouse/entry/
├── index.vue              # 主页面组件（列表展示、CRUD操作）
├── form.vue               # 表单组件（新增/编辑弹窗）
├── detail.vue             # 详情查看组件
├── ConfirmModal.vue       # 确认入库弹窗
├── CancelModal.vue        # 取消入库弹窗
├── BatchLinesModal.vue    # 批量录入明细弹窗
├── schemas.ts             # 配置文件（表格列、搜索表单、数据表单）
└── README.md              # 说明文档

src/api/warehouse/
├── entry.ts               # API接口定义
└── model/entryModel.ts    # 数据模型
```

## 主要功能

### 1. 入库单管理
- **新增入库单**：创建新的入库单，包含基本信息和明细信息
- **编辑入库单**：仅待确认状态的入库单可以编辑
- **删除入库单**：支持单个删除和批量删除（仅待确认状态）
- **查看详情**：查看入库单的完整信息和明细列表

### 2. 状态管理
- **待确认**：新创建的入库单，可以编辑、确认、取消、批量录入
- **已确认**：已确认的入库单，只能查看详情
- **已完成**：已完成的入库单，只能查看详情
- **已取消**：已取消的入库单，只能查看详情

### 3. 批量操作
- **批量录入明细**：支持手动添加和Excel文件导入
- **批量删除**：支持选择多个入库单进行批量删除
- **Excel模板下载**：提供标准的Excel导入模板

### 4. 搜索筛选
- 支持按入库单号、供应商名称关键词搜索
- 支持按云仓编码、订单状态、供应商编码筛选
- 支持按创建时间范围筛选

## 技术特性

### 1. 响应式设计
- 支持桌面端和移动端自适应
- 表格支持横向滚动
- 表单布局自动适配屏幕尺寸

### 2. 用户体验优化
- 加载状态提示
- 详细的错误信息提示
- 操作确认对话框
- 使用说明和帮助信息

### 3. 数据验证
- 前端表单验证
- 业务逻辑验证
- 状态权限控制

### 4. 性能优化
- 分页加载
- 按需渲染
- 防重复提交

## 使用说明

### 1. 创建入库单
1. 点击"新增入库单"按钮
2. 填写基本信息（云仓编码、供应商信息等）
3. 添加入库明细（商品编码、名称、数量等）
4. 点击确认保存

### 2. 批量录入明细
1. 在入库单列表中点击"批量录入"按钮
2. 可以手动添加行或下载Excel模板
3. 填写完整后导入Excel文件
4. 确认数据无误后提交

### 3. 确认入库
1. 在入库单列表中点击"确认"按钮
2. 填写实际到货时间和操作员信息
3. 确认后状态变更为"已确认"

### 4. 取消入库
1. 在入库单列表中点击"取消"按钮
2. 填写取消原因（必填）
3. 确认后状态变更为"已取消"

## API 接口

### 入库单管理
- `POST /warehouse/entry/order/create` - 创建入库单
- `PUT /warehouse/entry/order/update` - 更新入库单
- `GET /warehouse/entry/order/list` - 获取入库单列表
- `GET /warehouse/entry/order/detail/:id` - 获取入库单详情
- `PUT /warehouse/entry/order/confirm` - 确认入库
- `PUT /warehouse/entry/order/cancel` - 取消入库
- `DELETE /warehouse/entry/order/delete` - 删除入库单

### 明细管理
- `POST /warehouse/entry/lines/batch` - 批量录入明细

## 注意事项

1. **状态流转**：入库单状态流转是单向的，确认和取消操作不可逆
2. **权限控制**：不同状态下的操作权限不同，系统会自动控制按钮显示
3. **数据验证**：提交前会进行完整的数据验证，确保数据完整性
4. **Excel导入**：导入的Excel文件必须按照模板格式，否则可能导入失败
5. **批量操作**：批量删除只能操作待确认状态的入库单

## 扩展说明

该功能模块采用模块化设计，易于扩展和维护：
- 可以轻松添加新的状态和操作
- 支持自定义字段和验证规则
- 可以集成更多的批量操作功能
- 支持与其他业务模块的集成
