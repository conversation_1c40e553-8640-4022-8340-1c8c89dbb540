import type { VxeGridProps } from '#/adapter/vxe-table';

import { h } from 'vue';

import { type VbenFormProps } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { z } from '#/adapter/form';

// 订单状态枚举
export const ORDER_STATUS = {
  PENDING: 1,    // 待确认
  CONFIRMED: 2,  // 已确认
  COMPLETED: 3,  // 已完成
  CANCELLED: 4,  // 已取消
} as const;

// 订单状态标签配置
export const ORDER_STATUS_TAGS = {
  [ORDER_STATUS.PENDING]: { color: 'orange', text: '待确认' },
  [ORDER_STATUS.CONFIRMED]: { color: 'blue', text: '已确认' },
  [ORDER_STATUS.COMPLETED]: { color: 'green', text: '已完成' },
  [ORDER_STATUS.CANCELLED]: { color: 'red', text: '已取消' },
};

// 业务类型选项
export const ORDER_TYPE_OPTIONS = [
  { label: '采购入库', value: 'CGRK' },
  { label: '退货入库', value: 'THRK' },
  { label: '调拨入库', value: 'DBRK' },
  { label: '其他入库', value: 'QTRK' },
];

// 设备状态选项
export const DEVICE_STATUS_OPTIONS = [
  { label: '正常', value: 1 },
  { label: '损坏', value: 2 },
  { label: '待检', value: 3 },
];

// 状态流转规则
export const STATUS_FLOW_RULES = {
  [ORDER_STATUS.PENDING]: {
    allowedActions: ['confirm', 'cancel', 'edit', 'delete', 'batchLines'],
    nextStatuses: [ORDER_STATUS.CONFIRMED, ORDER_STATUS.CANCELLED],
  },
  [ORDER_STATUS.CONFIRMED]: {
    allowedActions: ['complete', 'view'],
    nextStatuses: [ORDER_STATUS.COMPLETED],
  },
  [ORDER_STATUS.COMPLETED]: {
    allowedActions: ['view'],
    nextStatuses: [],
  },
  [ORDER_STATUS.CANCELLED]: {
    allowedActions: ['view'],
    nextStatuses: [],
  },
};

// 检查操作权限
export function canPerformAction(orderStatus: number, action: string): boolean {
  const rules = STATUS_FLOW_RULES[orderStatus as keyof typeof STATUS_FLOW_RULES];
  return rules ? rules.allowedActions.includes(action) : false;
}

// 获取状态显示信息
export function getStatusInfo(status: number) {
  return ORDER_STATUS_TAGS[status as keyof typeof ORDER_STATUS_TAGS] ||
    { color: 'default', text: '未知状态' };
}

export const tableColumns: VxeGridProps = {
  columns: [
    {
      type: 'checkbox',
      width: 50,
    },
    {
      title: '入库单号',
      field: 'entryOrderCode',
      width: 160,
      slots: {
        default: ({ row }) => 
          h('span', { class: 'font-medium text-blue-600' }, row.entryOrderCode || '-'),
      },
    },
    {
      title: '云仓编码',
      field: 'warehouseCode',
      width: 120,
    },
    {
      title: '业务类型',
      field: 'orderType',
      width: 100,
      slots: {
        default: ({ row }) => {
          const typeOption = ORDER_TYPE_OPTIONS.find(opt => opt.value === row.orderType);
          return h(Tag, { color: 'blue' }, () => typeOption?.label || row.orderType);
        },
      },
    },
    {
      title: '供应商',
      field: 'supplierName',
      width: 150,
      showOverflow: 'tooltip',
    },
    {
      title: '采购单号',
      field: 'purchaseOrderCode',
      width: 140,
      showOverflow: 'tooltip',
    },
    {
      title: '预期到货时间',
      field: 'expectStartTime',
      width: 160,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm') : '-';
      },
    },
    {
      title: '实际到货时间',
      field: 'actualArrivalTime',
      width: 160,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm') : '-';
      },
    },
    {
      title: '订单状态',
      field: 'orderStatus',
      width: 100,
      slots: {
        default: ({ row }) => {
          const statusConfig = ORDER_STATUS_TAGS[row.orderStatus as keyof typeof ORDER_STATUS_TAGS];
          return h(Tag, { color: statusConfig?.color || 'default' }, () => statusConfig?.text || '未知');
        },
      },
    },
    {
      title: '操作员',
      field: 'operatorName',
      width: 100,
    },
    {
      title: $t('common.createTime'),
      field: 'createdAt',
      formatter: 'formatDateTime',
      width: 160,
    },
    {
      title: '操作',
      field: 'action',
      width: 200,
      fixed: 'right',
      slots: {
        default: ({ row }) => {
          const buttons = [
            h(
              Button,
              {
                type: 'link',
                size: 'small',
                onClick: () => {
                  // 查看详情逻辑
                  console.log('查看详情', row);
                },
              },
              () => '详情',
            ),
          ];

          if (row.orderStatus === ORDER_STATUS.PENDING) {
            buttons.push(
              h(
                Button,
                {
                  type: 'link',
                  size: 'small',
                  onClick: () => {
                    // 确认入库逻辑
                    console.log('确认入库', row);
                  },
                },
                () => '确认',
              ),
              h(
                Button,
                {
                  type: 'link',
                  size: 'small',
                  danger: true,
                  onClick: () => {
                    // 取消入库逻辑
                    console.log('取消入库', row);
                  },
                },
                () => '取消',
              ),
            );
          }

          return buttons;
        },
      },
    },
  ],
};

export const searchFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'keyword',
      label: '搜索关键词',
      component: 'Input',
      componentProps: {
        placeholder: '请输入入库单号、供应商名称',
      },
    },
    {
      fieldName: 'warehouseCode',
      label: '云仓编码',
      component: 'Input',
      componentProps: {
        placeholder: '请输入云仓编码',
      },
    },
    {
      fieldName: 'orderStatus',
      label: '订单状态',
      component: 'Select',
      componentProps: {
        options: [
          { label: '全部', value: 0 },
          { label: '待确认', value: ORDER_STATUS.PENDING },
          { label: '已确认', value: ORDER_STATUS.CONFIRMED },
          { label: '已完成', value: ORDER_STATUS.COMPLETED },
          { label: '已取消', value: ORDER_STATUS.CANCELLED },
        ],
        placeholder: '请选择订单状态',
      },
    },
    {
      fieldName: 'supplierCode',
      label: '供应商编码',
      component: 'Input',
      componentProps: {
        placeholder: '请输入供应商编码',
      },
    },
    {
      fieldName: 'timeRange',
      label: '创建时间',
      component: 'RangePicker',
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
      },
    },
  ],
};

export const dataFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'id',
      label: 'ID',
      component: 'Input',
      dependencies: {
        show: false,
        triggerFields: ['id'],
      },
    },
    {
      fieldName: 'warehouseCode',
      label: '云仓编码',
      component: 'Input',
      rules: z.string().min(1, '云仓编码不能为空'),
      componentProps: {
        placeholder: '请输入云仓编码',
      },
    },
    {
      fieldName: 'orderType',
      label: '业务类型',
      component: 'Select',
      rules: z.string().min(1, '业务类型不能为空'),
      componentProps: {
        options: ORDER_TYPE_OPTIONS,
        placeholder: '请选择业务类型',
      },
      defaultValue: 'CGRK',
    },
    {
      fieldName: 'supplierCode',
      label: '供应商编码',
      component: 'Input',
      rules: z.string().min(1, '供应商编码不能为空'),
      componentProps: {
        placeholder: '请输入供应商编码',
      },
    },
    {
      fieldName: 'supplierName',
      label: '供应商名称',
      component: 'Input',
      rules: z.string().min(1, '供应商名称不能为空'),
      componentProps: {
        placeholder: '请输入供应商名称',
      },
    },
    {
      fieldName: 'purchaseOrderCode',
      label: '采购单号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入采购单号（可选）',
      },
    },
    {
      fieldName: 'expectTimeRange',
      label: '预期到货时间',
      component: 'RangePicker',
      componentProps: {
        placeholder: ['预期开始时间', '预期结束时间'],
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
      },
    },
    {
      fieldName: 'logisticsName',
      label: '物流公司',
      component: 'Input',
      componentProps: {
        placeholder: '请输入物流公司名称（可选）',
      },
    },
    {
      fieldName: 'expressCode',
      label: '运单号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入运单号（可选）',
      },
    },
    {
      fieldName: 'operatorName',
      label: '操作员',
      component: 'Input',
      componentProps: {
        placeholder: '请输入操作员姓名（可选）',
      },
    },
    {
      fieldName: 'remark',
      label: '备注',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入备注信息（可选）',
        rows: 3,
      },
    },
  ],
};

// 入库明细表单配置
export const entryLineFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'itemCode',
      label: '商品编码',
      component: 'Input',
      rules: z.string().min(1, '商品编码不能为空'),
      componentProps: {
        placeholder: '请输入商品编码（设备序列号）',
      },
    },
    {
      fieldName: 'itemName',
      label: '商品名称',
      component: 'Input',
      rules: z.string().min(1, '商品名称不能为空'),
      componentProps: {
        placeholder: '请输入商品名称',
      },
    },
    {
      fieldName: 'planQty',
      label: '计划数量',
      component: 'InputNumber',
      rules: z.number().min(1, '计划数量必须大于0'),
      componentProps: {
        placeholder: '请输入计划入库数量',
        min: 1,
      },
      defaultValue: 1,
    },
    {
      fieldName: 'purchasePrice',
      label: '采购价格',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入采购价格（可选）',
        min: 0,
        precision: 2,
        addonAfter: '元',
      },
    },
    {
      fieldName: 'retailPrice',
      label: '零售价格',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入零售价格（可选）',
        min: 0,
        precision: 2,
        addonAfter: '元',
      },
    },
    {
      fieldName: 'batchCode',
      label: '批次编码',
      component: 'Input',
      componentProps: {
        placeholder: '请输入批次编码（可选）',
      },
    },
    {
      fieldName: 'produceCode',
      label: '生产批号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入生产批号（可选）',
      },
    },
    {
      fieldName: 'productDate',
      label: '生产日期',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择生产日期（可选）',
        format: 'YYYY-MM-DD',
      },
    },
    {
      fieldName: 'expireDate',
      label: '过期日期',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择过期日期（可选）',
        format: 'YYYY-MM-DD',
      },
    },
    {
      fieldName: 'boxNumber',
      label: '箱号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入箱号（可选）',
      },
    },
    {
      fieldName: 'palletNumber',
      label: '卡板号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入卡板号（可选）',
      },
    },
    {
      fieldName: 'remark',
      label: '备注',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入备注信息（可选）',
        rows: 2,
      },
    },
  ],
};

// 确认入库表单配置
export const confirmFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'actualArrivalTime',
      label: '实际到货时间',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择实际到货时间',
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
      },
    },
    {
      fieldName: 'operatorName',
      label: '操作员',
      component: 'Input',
      componentProps: {
        placeholder: '请输入操作员姓名',
      },
    },
    {
      fieldName: 'remark',
      label: '确认备注',
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入确认备注（可选）',
        rows: 3,
      },
    },
  ],
};

// 取消入库表单配置
export const cancelFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'cancelReason',
      label: '取消原因',
      component: 'Textarea',
      rules: z.string().min(1, '取消原因不能为空'),
      componentProps: {
        placeholder: '请输入取消原因',
        rows: 4,
      },
    },
    {
      fieldName: 'operatorName',
      label: '操作员',
      component: 'Input',
      componentProps: {
        placeholder: '请输入操作员姓名',
      },
    },
  ],
};
