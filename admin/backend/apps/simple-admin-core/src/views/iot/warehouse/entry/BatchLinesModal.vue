<script lang="ts" setup>
import type { EntryOrderInfo } from '#/api/warehouse/model/entryModel';

import { batchCreateEntryLines } from '#/api/warehouse/entry';
import { useVbenModal } from '@vben/common-ui';
import { Button, Card, message, Upload, Table, Space } from 'ant-design-vue';
import { PlusOutlined, DeleteOutlined, UploadOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import { ref, reactive, h } from 'vue';
import * as XLSX from 'xlsx';

defineOptions({
  name: 'BatchEntryLinesModal',
});

const record = ref<EntryOrderInfo>();
const loading = ref(false);

// 批量明细数据
const batchLines = ref<any[]>([]);

// 表格列配置
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 60,
    customRender: ({ index }: { index: number }) => index + 1,
  },
  {
    title: '商品编码',
    dataIndex: 'itemCode',
    key: 'itemCode',
    width: 150,
  },
  {
    title: '商品名称',
    dataIndex: 'itemName',
    key: 'itemName',
    width: 200,
  },
  {
    title: '计划数量',
    dataIndex: 'planQty',
    key: 'planQty',
    width: 100,
  },
  {
    title: '采购价格',
    dataIndex: 'purchasePrice',
    key: 'purchasePrice',
    width: 100,
    customRender: ({ record }: { record: any }) => 
      record.purchasePrice ? `¥${record.purchasePrice.toFixed(2)}` : '-',
  },
  {
    title: '零售价格',
    dataIndex: 'retailPrice',
    key: 'retailPrice',
    width: 100,
    customRender: ({ record }: { record: any }) => 
      record.retailPrice ? `¥${record.retailPrice.toFixed(2)}` : '-',
  },
  {
    title: '批次编码',
    dataIndex: 'batchCode',
    key: 'batchCode',
    width: 120,
    customRender: ({ record }: { record: any }) => record.batchCode || '-',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    ellipsis: true,
    customRender: ({ record }: { record: any }) => record.remark || '-',
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    customRender: ({ index }: { index: number }) =>
      h(
        Button,
        {
          type: 'link',
          danger: true,
          size: 'small',
          onClick: () => removeLine(index),
        },
        () => '删除',
      ),
  },
];

// 添加空行
function addEmptyLine() {
  batchLines.value.push({
    itemCode: '',
    itemName: '',
    planQty: 1,
    purchasePrice: undefined,
    retailPrice: undefined,
    batchCode: '',
    produceCode: '',
    productDate: '',
    expireDate: '',
    boxNumber: '',
    palletNumber: '',
    remark: '',
  });
}

// 删除行
function removeLine(index: number) {
  batchLines.value.splice(index, 1);
}

// 清空数据
function clearLines() {
  batchLines.value = [];
}

// 下载模板
function downloadTemplate() {
  const templateData = [
    {
      '商品编码': 'ITEM001',
      '商品名称': '示例商品',
      '计划数量': 1,
      '采购价格': 100.00,
      '零售价格': 150.00,
      '批次编码': 'BATCH001',
      '生产批号': 'PROD001',
      '生产日期': '2024-01-01',
      '过期日期': '2025-01-01',
      '箱号': 'BOX001',
      '卡板号': 'PALLET001',
      '备注': '示例备注',
    },
  ];

  const ws = XLSX.utils.json_to_sheet(templateData);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, '入库明细模板');
  XLSX.writeFile(wb, '入库明细导入模板.xlsx');
}

// 处理文件上传
function handleFileUpload(file: File) {
  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target?.result as ArrayBuffer);
      const workbook = XLSX.read(data, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      // 转换数据格式
      const convertedData = jsonData.map((row: any) => ({
        itemCode: row['商品编码'] || '',
        itemName: row['商品名称'] || '',
        planQty: Number(row['计划数量']) || 1,
        purchasePrice: row['采购价格'] ? Number(row['采购价格']) : undefined,
        retailPrice: row['零售价格'] ? Number(row['零售价格']) : undefined,
        batchCode: row['批次编码'] || '',
        produceCode: row['生产批号'] || '',
        productDate: row['生产日期'] || '',
        expireDate: row['过期日期'] || '',
        boxNumber: row['箱号'] || '',
        palletNumber: row['卡板号'] || '',
        remark: row['备注'] || '',
      }));

      batchLines.value = convertedData;
      message.success(`成功导入 ${convertedData.length} 条明细数据`);
    } catch (error) {
      message.error('文件解析失败，请检查文件格式');
    }
  };
  reader.readAsArrayBuffer(file);
  return false; // 阻止默认上传行为
}

// 验证数据
function validateData() {
  if (batchLines.value.length === 0) {
    message.error('请至少添加一条明细数据');
    return false;
  }

  for (let i = 0; i < batchLines.value.length; i++) {
    const line = batchLines.value[i];
    if (!line.itemCode || !line.itemName || !line.planQty) {
      message.error(`第${i + 1}行数据不完整，请检查商品编码、商品名称和计划数量`);
      return false;
    }
  }
  return true;
}

// 提交数据
async function handleSubmit() {
  if (!validateData() || !record.value?.id) {
    return;
  }

  loading.value = true;
  try {
    const result = await batchCreateEntryLines({
      entryOrderId: record.value.id,
      orderLines: batchLines.value,
    });

    if (result.code === 0) {
      const { successCount, failCount, failDetails } = result.data.data;
      if (failCount > 0) {
        message.warning(`批量录入完成：成功 ${successCount} 条，失败 ${failCount} 条`);
        if (failDetails && failDetails.length > 0) {
          console.warn('失败详情:', failDetails);
        }
      } else {
        message.success(`批量录入成功，共录入 ${successCount} 条明细`);
      }
    } else {
      message.error(result.msg || '批量录入失败');
    }
  } catch (error) {
    message.error('批量录入失败，请重试');
  } finally {
    loading.value = false;
  }
}

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: true,
  width: '90%',
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    if (loading.value) return;

    await handleSubmit();
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData();
      record.value = data?.record;
      batchLines.value = [];
      addEmptyLine(); // 默认添加一行
    }
    
    modalApi.setState({
      title: `批量录入明细 - ${record.value?.entryOrderCode || ''}`,
    });
  },
});

defineExpose(modalApi);
</script>

<template>
  <Modal>
    <div class="space-y-4">
      <!-- 操作工具栏 -->
      <Card size="small">
        <Space>
          <Button type="primary" @click="addEmptyLine">
            <PlusOutlined />
            添加行
          </Button>
          
          <Upload
            :before-upload="handleFileUpload"
            :show-upload-list="false"
            accept=".xlsx,.xls"
          >
            <Button>
              <UploadOutlined />
              导入Excel
            </Button>
          </Upload>
          
          <Button @click="downloadTemplate">
            <DownloadOutlined />
            下载模板
          </Button>
          
          <Button danger @click="clearLines">
            <DeleteOutlined />
            清空数据
          </Button>
        </Space>
      </Card>

      <!-- 数据表格 -->
      <Card title="明细数据" size="small">
        <Table
          :columns="columns"
          :data-source="batchLines"
          :pagination="false"
          size="small"
          :scroll="{ x: 1000 }"
          row-key="index"
        >
          <template #emptyText>
            <div class="text-center py-8 text-gray-500">
              暂无数据，请点击"添加行"或"导入Excel"添加明细数据
            </div>
          </template>
        </Table>
      </Card>

      <!-- 提示信息 -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">
              批量录入说明
            </h3>
            <div class="mt-2 text-sm text-blue-700">
              <ul class="list-disc list-inside space-y-1">
                <li>可以手动添加行或通过Excel文件批量导入</li>
                <li>商品编码、商品名称、计划数量为必填项</li>
                <li>支持下载Excel模板，按模板格式填写后导入</li>
                <li>导入的数据会覆盖当前表格中的数据</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>
