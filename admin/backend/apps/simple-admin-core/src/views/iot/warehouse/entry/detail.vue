<script lang="ts" setup>
import type { EntryOrderInfo } from '#/api/warehouse/model/entryModel';

import { useVbenModal } from '@vben/common-ui';
import { Card, Descriptions, Table, Tag, Button, Space } from 'ant-design-vue';
import { ref, computed } from 'vue';
import dayjs from 'dayjs';

import { getEntryOrderDetail } from '#/api/warehouse/entry';
import { ORDER_STATUS_TAGS, ORDER_TYPE_OPTIONS, DEVICE_STATUS_OPTIONS } from './schemas';

defineOptions({
  name: 'EntryOrderDetail',
});

const record = ref<EntryOrderInfo>();
const loading = ref(false);

// 明细表格列配置
const detailColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 60,
    customRender: ({ index }: { index: number }) => index + 1,
  },
  {
    title: '商品编码',
    dataIndex: 'itemCode',
    key: 'itemCode',
    width: 150,
  },
  {
    title: '商品名称',
    dataIndex: 'itemName',
    key: 'itemName',
    width: 200,
  },
  {
    title: '计划数量',
    dataIndex: 'planQty',
    key: 'planQty',
    width: 100,
  },
  {
    title: '实际数量',
    dataIndex: 'actualQty',
    key: 'actualQty',
    width: 100,
    customRender: ({ record }: { record: any }) => record.actualQty || '-',
  },
  {
    title: '采购价格',
    dataIndex: 'purchasePrice',
    key: 'purchasePrice',
    width: 100,
    customRender: ({ record }: { record: any }) => 
      record.purchasePrice ? `¥${record.purchasePrice.toFixed(2)}` : '-',
  },
  {
    title: '零售价格',
    dataIndex: 'retailPrice',
    key: 'retailPrice',
    width: 100,
    customRender: ({ record }: { record: any }) => 
      record.retailPrice ? `¥${record.retailPrice.toFixed(2)}` : '-',
  },
  {
    title: '批次编码',
    dataIndex: 'batchCode',
    key: 'batchCode',
    width: 120,
    customRender: ({ record }: { record: any }) => record.batchCode || '-',
  },
  {
    title: '上架位置',
    dataIndex: 'shelfLocation',
    key: 'shelfLocation',
    width: 120,
    customRender: ({ record }: { record: any }) => record.shelfLocation || '-',
  },
  {
    title: '设备状态',
    dataIndex: 'deviceStatus',
    key: 'deviceStatus',
    width: 100,
    customRender: ({ record }: { record: any }) => {
      if (!record.deviceStatus) return '-';
      const status = DEVICE_STATUS_OPTIONS.find(opt => opt.value === record.deviceStatus);
      return status ? status.label : '-';
    },
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    ellipsis: true,
    customRender: ({ record }: { record: any }) => record.remark || '-',
  },
];

// 获取业务类型显示文本
const orderTypeText = computed(() => {
  if (!record.value?.orderType) return '-';
  const typeOption = ORDER_TYPE_OPTIONS.find(opt => opt.value === record.value.orderType);
  return typeOption?.label || record.value.orderType;
});

// 获取状态标签配置
const statusConfig = computed(() => {
  if (!record.value?.orderStatus) return { color: 'default', text: '未知' };
  return ORDER_STATUS_TAGS[record.value.orderStatus as keyof typeof ORDER_STATUS_TAGS] || 
    { color: 'default', text: '未知' };
});

// 加载详情数据
async function loadDetail(entryOrderId: number) {
  loading.value = true;
  try {
    const result = await getEntryOrderDetail({ id: entryOrderId });
    if (result.code === 0) {
      record.value = result.data.data;
    }
  } catch (error) {
    console.error('加载入库单详情失败:', error);
  } finally {
    loading.value = false;
  }
}

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: true,
  width: '90%',
  onCancel() {
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData();
      if (data?.record?.id) {
        loadDetail(data.record.id);
      } else {
        record.value = data?.record;
      }
    }
  },
});

defineExpose(modalApi);
</script>

<template>
  <Modal :title="`入库单详情 - ${record?.entryOrderCode || ''}`">
    <div v-if="record" class="space-y-6">
      <!-- 基本信息 -->
      <Card title="基本信息" size="small">
        <Descriptions :column="3" bordered size="small">
          <Descriptions.Item label="入库单号">
            <span class="font-medium text-blue-600">{{ record.entryOrderCode || '-' }}</span>
          </Descriptions.Item>
          <Descriptions.Item label="云仓编码">
            {{ record.warehouseCode || '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="业务类型">
            <Tag color="blue">{{ orderTypeText }}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="供应商编码">
            {{ record.supplierCode || '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="供应商名称">
            {{ record.supplierName || '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="订单状态">
            <Tag :color="statusConfig.color">{{ statusConfig.text }}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="采购单号">
            {{ record.purchaseOrderCode || '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="物流公司">
            {{ record.logisticsName || '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="运单号">
            {{ record.expressCode || '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="预期到货开始时间">
            {{ record.expectStartTime ? dayjs(record.expectStartTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="预期到货结束时间">
            {{ record.expectEndTime ? dayjs(record.expectEndTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="实际到货时间">
            {{ record.actualArrivalTime ? dayjs(record.actualArrivalTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="操作员">
            {{ record.operatorName || '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {{ record.createdAt ? dayjs(record.createdAt * 1000).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {{ record.updatedAt ? dayjs(record.updatedAt * 1000).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </Descriptions.Item>
          <Descriptions.Item label="备注" :span="3">
            {{ record.remark || '-' }}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <!-- 入库明细 -->
      <Card title="入库明细" size="small">
        <Table
          :columns="detailColumns"
          :data-source="record.orderLines || []"
          :pagination="false"
          size="small"
          :scroll="{ x: 1200 }"
          row-key="id"
        >
          <template #emptyText>
            <div class="text-center py-8 text-gray-500">
              暂无明细数据
            </div>
          </template>
        </Table>
      </Card>
    </div>

    <div v-else class="flex items-center justify-center py-12">
      <div class="text-gray-500">加载中...</div>
    </div>
  </Modal>
</template>
