<script lang="ts" setup>
import type { EntryOrderInfo } from '#/api/warehouse/model/entryModel';

import { useVbenForm } from '#/adapter/form';
import { createEntryOrder, updateEntryOrder, getEntryOrderDetail } from '#/api/warehouse/entry';
import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { Button, Card, Divider, message, Space } from 'ant-design-vue';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { ref, reactive, computed } from 'vue';
import dayjs from 'dayjs';

import { dataFormSchemas, entryLineFormSchemas } from './schemas';

defineOptions({
  name: 'EntryOrderForm',
});

const record = ref();
const isUpdate = ref(false);
const gridApi = ref();
const loading = ref(false);

// 入库明细数据
const entryLines = ref<any[]>([]);

// 添加明细行
function addEntryLine() {
  entryLines.value.push({
    id: Date.now(), // 临时ID
    itemCode: '',
    itemName: '',
    planQty: 1,
    purchasePrice: undefined,
    retailPrice: undefined,
    batchCode: '',
    produceCode: '',
    productDate: '',
    expireDate: '',
    boxNumber: '',
    palletNumber: '',
    remark: '',
  });
}

// 删除明细行
function removeEntryLine(index: number) {
  entryLines.value.splice(index, 1);
}

// 验证明细数据
function validateEntryLines() {
  if (entryLines.value.length === 0) {
    message.error('请至少添加一条入库明细');
    return false;
  }

  for (let i = 0; i < entryLines.value.length; i++) {
    const line = entryLines.value[i];

    if (!line.itemCode || line.itemCode.trim() === '') {
      message.error(`第${i + 1}行：商品编码不能为空`);
      return false;
    }

    if (!line.itemName || line.itemName.trim() === '') {
      message.error(`第${i + 1}行：商品名称不能为空`);
      return false;
    }

    if (!line.planQty || line.planQty <= 0) {
      message.error(`第${i + 1}行：计划数量必须大于0`);
      return false;
    }

    // 验证价格格式
    if (line.purchasePrice && (isNaN(line.purchasePrice) || line.purchasePrice < 0)) {
      message.error(`第${i + 1}行：采购价格格式不正确`);
      return false;
    }

    if (line.retailPrice && (isNaN(line.retailPrice) || line.retailPrice < 0)) {
      message.error(`第${i + 1}行：零售价格格式不正确`);
      return false;
    }
  }
  return true;
}

async function onSubmit(values: Record<string, any>) {
  if (!validateEntryLines()) {
    return;
  }

  // 验证基本信息
  if (!values.warehouseCode || !values.supplierCode || !values.supplierName) {
    message.error('请填写完整的基本信息');
    return;
  }

  // 处理时间范围
  const expectTimeRange = values.expectTimeRange;
  if (expectTimeRange && expectTimeRange.length === 2) {
    values.expectStartTime = dayjs(expectTimeRange[0]).format('YYYY-MM-DD HH:mm:ss');
    values.expectEndTime = dayjs(expectTimeRange[1]).format('YYYY-MM-DD HH:mm:ss');
  }
  delete values.expectTimeRange;

  // 处理明细数据
  const orderLines = entryLines.value.map(line => ({
    itemCode: line.itemCode.trim(),
    itemName: line.itemName.trim(),
    planQty: Number(line.planQty) || 1,
    purchasePrice: line.purchasePrice ? Number(line.purchasePrice) : undefined,
    retailPrice: line.retailPrice ? Number(line.retailPrice) : undefined,
    batchCode: line.batchCode ? line.batchCode.trim() : undefined,
    produceCode: line.produceCode ? line.produceCode.trim() : undefined,
    productDate: line.productDate ? dayjs(line.productDate).format('YYYY-MM-DD') : undefined,
    expireDate: line.expireDate ? dayjs(line.expireDate).format('YYYY-MM-DD') : undefined,
    boxNumber: line.boxNumber ? line.boxNumber.trim() : undefined,
    palletNumber: line.palletNumber ? line.palletNumber.trim() : undefined,
    remark: line.remark ? line.remark.trim() : undefined,
  }));

  const submitData = {
    ...values,
    orderLines,
  };

  loading.value = true;
  try {
    const result = isUpdate.value
      ? await updateEntryOrder(submitData)
      : await createEntryOrder(submitData);

    if (result.code === 0) {
      message.success(isUpdate.value ? '入库单更新成功' : '入库单创建成功');
      gridApi.value?.reload();
    } else {
      message.error(result.msg || (isUpdate.value ? '更新失败' : '创建失败'));
    }
  } catch (error) {
    console.error('提交失败:', error);
    message.error(isUpdate.value ? '更新失败，请重试' : '创建失败，请重试');
  } finally {
    loading.value = false;
  }
}

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [...(dataFormSchemas.schema as any)],
  showDefaultActions: false,
  layout: 'vertical',
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: true,
  width: '90%',
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    if (loading.value) return;

    const validationResult = await formApi.validate();
    if (validationResult.valid) {
      await formApi.submitForm();
      modalApi.close();
    }
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      // 重置加载状态
      loading.value = false;

      isUpdate.value = modalApi.getData()?.isUpdate;
      record.value = modalApi.getData()?.record || {};
      gridApi.value = modalApi.getData()?.gridApi;

      formApi.setValues(record.value);

      // 处理时间范围回显
      if (record.value.expectStartTime && record.value.expectEndTime) {
        formApi.setFieldValue('expectTimeRange', [
          dayjs(record.value.expectStartTime),
          dayjs(record.value.expectEndTime),
        ]);
      }

      // 加载明细数据
      if (record.value.id) {
        loadEntryLines(record.value.id);
      } else {
        entryLines.value = [];
        addEntryLine(); // 默认添加一行
      }

      modalApi.setState({
        title: isUpdate.value ? '编辑入库单' : '新增入库单',
      });
    }
  },
});

// 加载入库明细
async function loadEntryLines(entryOrderId: number) {
  try {
    const result = await getEntryOrderDetail({ id: entryOrderId });
    if (result.code === 0 && result.data.data.orderLines) {
      entryLines.value = result.data.data.orderLines.map(line => ({
        ...line,
        productDate: line.productDate ? dayjs(line.productDate) : '',
        expireDate: line.expireDate ? dayjs(line.expireDate) : '',
      }));
    }
  } catch (error) {
    console.error('加载入库明细失败:', error);
  }
}

defineExpose(modalApi);
</script>

<template>
  <Modal>
    <div class="space-y-6">
      <!-- 基本信息 -->
      <Card title="基本信息" size="small">
        <Form />
      </Card>

      <!-- 入库明细 -->
      <Card size="small">
        <template #title>
          <div class="flex items-center justify-between">
            <span>入库明细</span>
            <Button type="primary" size="small" @click="addEntryLine">
              <PlusOutlined />
              添加明细
            </Button>
          </div>
        </template>

        <div class="space-y-4">
          <div
            v-for="(line, index) in entryLines"
            :key="line.id"
            class="border border-gray-200 rounded-lg p-4"
          >
            <div class="flex items-center justify-between mb-3">
              <span class="font-medium text-gray-700">明细 {{ index + 1 }}</span>
              <Button
                v-if="entryLines.length > 1"
                type="text"
                danger
                size="small"
                @click="removeEntryLine(index)"
              >
                <DeleteOutlined />
                删除
              </Button>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  商品编码 <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="line.itemCode"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入商品编码"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  商品名称 <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="line.itemName"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入商品名称"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  计划数量 <span class="text-red-500">*</span>
                </label>
                <input
                  v-model.number="line.planQty"
                  type="number"
                  min="1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入计划数量"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">采购价格</label>
                <input
                  v-model.number="line.purchasePrice"
                  type="number"
                  min="0"
                  step="0.01"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入采购价格"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">零售价格</label>
                <input
                  v-model.number="line.retailPrice"
                  type="number"
                  min="0"
                  step="0.01"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入零售价格"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">批次编码</label>
                <input
                  v-model="line.batchCode"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入批次编码"
                />
              </div>
            </div>

            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
              <textarea
                v-model="line.remark"
                rows="2"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入备注信息"
              />
            </div>
          </div>

          <div v-if="entryLines.length === 0" class="text-center py-8 text-gray-500">
            暂无明细数据，请点击"添加明细"按钮添加
          </div>
        </div>
      </Card>
    </div>
  </Modal>
</template>
