<script lang="ts" setup>
import type { EntryOrderInfo } from '#/api/warehouse/model/entryModel';

import { useVbenForm } from '#/adapter/form';
import { confirmEntryOrder } from '#/api/warehouse/entry';
import { useVbenModal } from '@vben/common-ui';
import { message } from 'ant-design-vue';
import { ref } from 'vue';
import dayjs from 'dayjs';

import { confirmFormSchemas } from './schemas';

defineOptions({
  name: 'ConfirmEntryOrderModal',
});

const record = ref<EntryOrderInfo>();

async function onSubmit(values: Record<string, any>) {
  if (!record.value?.id) {
    message.error('入库单信息错误');
    return;
  }

  // 状态验证
  if (record.value.orderStatus !== 1) {
    message.error('只有待确认状态的入库单才能进行确认操作');
    return;
  }

  try {
    const submitData = {
      id: record.value.id,
      actualArrivalTime: values.actualArrivalTime ?
        dayjs(values.actualArrivalTime).format('YYYY-MM-DD HH:mm:ss') : undefined,
      operatorName: values.operatorName,
      remark: values.remark,
    };

    const result = await confirmEntryOrder(submitData);

    if (result.code === 0) {
      message.success('确认入库成功');
    } else {
      message.error(result.msg || '确认入库失败');
    }
  } catch (error) {
    message.error('确认入库失败，请重试');
  }
}

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [...(confirmFormSchemas.schema as any)],
  showDefaultActions: false,
  layout: 'vertical',
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    const validationResult = await formApi.validate();
    if (validationResult.valid) {
      await formApi.submitForm();
      modalApi.close();
    }
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData();
      record.value = data?.record;
      
      // 设置默认值
      formApi.setValues({
        actualArrivalTime: new Date(),
        operatorName: '',
        remark: '',
      });
    }
    
    modalApi.setState({
      title: `确认入库 - ${record.value?.entryOrderCode || ''}`,
    });
  },
});

defineExpose(modalApi);
</script>

<template>
  <Modal>
    <div class="space-y-4">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">
              确认入库操作
            </h3>
            <div class="mt-2 text-sm text-blue-700">
              <p>确认后，入库单状态将变更为"已确认"，请仔细核对相关信息。</p>
            </div>
          </div>
        </div>
      </div>
      
      <Form />
    </div>
  </Modal>
</template>
