<script lang="ts" setup>
import type { EntryOrderInfo } from '#/api/warehouse/model/entryModel';

import { useVbenForm } from '#/adapter/form';
import { cancelEntryOrder } from '#/api/warehouse/entry';
import { useVbenModal } from '@vben/common-ui';
import { message } from 'ant-design-vue';
import { ref } from 'vue';

import { cancelFormSchemas } from './schemas';

defineOptions({
  name: 'CancelEntryOrderModal',
});

const record = ref<EntryOrderInfo>();

async function onSubmit(values: Record<string, any>) {
  if (!record.value?.id) {
    message.error('入库单信息错误');
    return;
  }

  // 状态验证
  if (record.value.orderStatus !== 1) {
    message.error('只有待确认状态的入库单才能进行取消操作');
    return;
  }

  // 验证取消原因
  if (!values.cancelReason || values.cancelReason.trim() === '') {
    message.error('请输入取消原因');
    return;
  }

  try {
    const result = await cancelEntryOrder({
      id: record.value.id,
      cancelReason: values.cancelReason.trim(),
      operatorName: values.operatorName,
    });

    if (result.code === 0) {
      message.success('取消入库成功');
    } else {
      message.error(result.msg || '取消入库失败');
    }
  } catch (error) {
    message.error('取消入库失败，请重试');
  }
}

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [...(cancelFormSchemas.schema as any)],
  showDefaultActions: false,
  layout: 'vertical',
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    const validationResult = await formApi.validate();
    if (validationResult.valid) {
      await formApi.submitForm();
      modalApi.close();
    }
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData();
      record.value = data?.record;
      
      // 清空表单
      formApi.setValues({
        cancelReason: '',
        operatorName: '',
      });
    }
    
    modalApi.setState({
      title: `取消入库 - ${record.value?.entryOrderCode || ''}`,
    });
  },
});

defineExpose(modalApi);
</script>

<template>
  <Modal>
    <div class="space-y-4">
      <div class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">
              取消入库操作
            </h3>
            <div class="mt-2 text-sm text-red-700">
              <p>取消后，入库单状态将变更为"已取消"，此操作不可恢复，请谨慎操作。</p>
            </div>
          </div>
        </div>
      </div>
      
      <Form />
    </div>
  </Modal>
</template>
