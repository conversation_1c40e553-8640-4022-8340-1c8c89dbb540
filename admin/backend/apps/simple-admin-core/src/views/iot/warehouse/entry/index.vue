<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { EntryOrderInfo } from '#/api/warehouse/model/entryModel';

import { h, ref } from 'vue';

import { Page, useVbenModal, type VbenFormProps } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, Modal, message, Space } from 'ant-design-vue';
import { isPlainObject } from 'remeda';
import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { 
  batchDeleteEntryOrder, 
  getEntryOrderList,
  confirmEntryOrder,
  cancelEntryOrder,
} from '#/api/warehouse/entry';

import EntryOrderForm from './form.vue';
import EntryOrderDetail from './detail.vue';
import ConfirmModal from './ConfirmModal.vue';
import CancelModal from './CancelModal.vue';
import BatchLinesModal from './BatchLinesModal.vue';
import { searchFormSchemas, tableColumns, ORDER_STATUS, canPerformAction } from './schemas';

defineOptions({
  name: 'EntryOrderManagement',
});

// ---------------- form -----------------

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: EntryOrderForm,
});

const [DetailModal, detailModalApi] = useVbenModal({
  connectedComponent: EntryOrderDetail,
});

const [ConfirmModalComponent, confirmModalApi] = useVbenModal({
  connectedComponent: ConfirmModal,
  onBeforeClose: () => {
    gridApi.reload();
    return true;
  },
});

const [CancelModalComponent, cancelModalApi] = useVbenModal({
  connectedComponent: CancelModal,
  onBeforeClose: () => {
    gridApi.reload();
    return true;
  },
});

const [BatchLinesModalComponent, batchLinesModalApi] = useVbenModal({
  connectedComponent: BatchLinesModal,
  onBeforeClose: () => {
    gridApi.reload();
    return true;
  },
});

const showDeleteButton = ref<boolean>(false);

const gridEvents: VxeGridListeners<any> = {
  checkboxChange(e) {
    showDeleteButton.value = e.$table.getCheckboxRecords().length > 0;
  },
  checkboxAll(e) {
    showDeleteButton.value = e.$table.getCheckboxRecords().length > 0;
  },
};

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [...(searchFormSchemas.schema as any)],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5',
};

// ------------- table --------------------

// 更新表格列配置，添加操作按钮
const updatedTableColumns = {
  ...tableColumns,
  columns: tableColumns.columns?.map(col => {
    if (col.field === 'action') {
      return {
        ...col,
        slots: {
          default: ({ row }: { row: EntryOrderInfo }) => {
            const buttons = [];

            // 详情按钮 - 所有状态都可以查看
            buttons.push(
              h(
                Button,
                {
                  type: 'link',
                  size: 'small',
                  onClick: () => openDetailModal(row),
                },
                () => '详情',
              ),
            );

            // 确认按钮 - 仅待确认状态可用
            if (canPerformAction(row.orderStatus || 0, 'confirm')) {
              buttons.push(
                h(
                  Button,
                  {
                    type: 'link',
                    size: 'small',
                    onClick: () => openConfirmModal(row),
                  },
                  () => '确认',
                ),
              );
            }

            // 取消按钮 - 仅待确认状态可用
            if (canPerformAction(row.orderStatus || 0, 'cancel')) {
              buttons.push(
                h(
                  Button,
                  {
                    type: 'link',
                    size: 'small',
                    danger: true,
                    onClick: () => openCancelModal(row),
                  },
                  () => '取消',
                ),
              );
            }

            // 编辑按钮 - 仅待确认状态可用
            if (canPerformAction(row.orderStatus || 0, 'edit')) {
              buttons.push(
                h(
                  Button,
                  {
                    type: 'link',
                    size: 'small',
                    onClick: () => openFormModal(row),
                  },
                  () => '编辑',
                ),
              );
            }

            // 批量录入按钮 - 仅待确认状态可用
            if (canPerformAction(row.orderStatus || 0, 'batchLines')) {
              buttons.push(
                h(
                  Button,
                  {
                    type: 'link',
                    size: 'small',
                    onClick: () => openBatchLinesModal(row),
                  },
                  () => '批量录入',
                ),
              );
            }

            return buttons;
          },
        },
      };
    }
    return col;
  }),
};

const gridOptions: VxeGridProps<EntryOrderInfo> = {
  columns: updatedTableColumns.columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        // 处理时间范围参数
        const params: any = {
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        };

        if (formValues.timeRange && formValues.timeRange.length === 2) {
          params.startTime = dayjs(formValues.timeRange[0]).format('YYYY-MM-DD HH:mm:ss');
          params.endTime = dayjs(formValues.timeRange[1]).format('YYYY-MM-DD HH:mm:ss');
          delete params.timeRange;
        }

        const res = await getEntryOrderList(params);
        return res.data.data;
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

function openFormModal(record: any) {
  if (isPlainObject(record)) {
    formModalApi.setData({
      record,
      isUpdate: true,
      gridApi,
    });
  } else {
    formModalApi.setData({
      record: null,
      isUpdate: false,
      gridApi,
    });
  }
  formModalApi.open();
}

function openDetailModal(record: EntryOrderInfo) {
  detailModalApi.setData({ record });
  detailModalApi.open();
}

function openConfirmModal(record: EntryOrderInfo) {
  confirmModalApi.setData({ record });
  confirmModalApi.open();
}

function openCancelModal(record: EntryOrderInfo) {
  cancelModalApi.setData({ record });
  cancelModalApi.open();
}

function openBatchLinesModal(record: EntryOrderInfo) {
  batchLinesModalApi.setData({ record });
  batchLinesModalApi.open();
}

function handleBatchDelete() {
  const selectedRecords = gridApi.grid.getCheckboxRecords();

  // 检查是否有不可删除的记录
  const undeletableRecords = selectedRecords.filter((record: any) =>
    !canPerformAction(record.orderStatus || 0, 'delete')
  );

  if (undeletableRecords.length > 0) {
    message.warning('选中的记录中包含不可删除的入库单（非待确认状态），请重新选择');
    return;
  }

  Modal.confirm({
    title: $t('common.deleteConfirm'),
    content: '确定要删除选中的入库单吗？此操作不可恢复。',
    async onOk() {
      const ids = selectedRecords.map((item: any) => item.id);
      await batchDelete(ids);
    },
  });
}

async function batchDelete(ids: any[]) {
  try {
    const result = await batchDeleteEntryOrder({ ids });
    if (result.code === 0) {
      message.success('删除成功');
      await gridApi.reload();
      showDeleteButton.value = false;
    } else {
      message.error(result.msg || '删除失败');
    }
  } catch (error) {
    message.error('删除失败，请重试');
  }
}

function showHelp() {
  Modal.info({
    title: '入库管理使用说明',
    width: 600,
    content: h('div', { class: 'space-y-4' }, [
      h('div', [
        h('h4', { class: 'font-medium mb-2' }, '功能说明：'),
        h('ul', { class: 'list-disc list-inside space-y-1 text-sm' }, [
          h('li', '新增入库单：创建新的入库单，包含基本信息和明细信息'),
          h('li', '编辑入库单：仅待确认状态的入库单可以编辑'),
          h('li', '确认入库：将待确认状态的入库单确认为已确认状态'),
          h('li', '取消入库：将待确认状态的入库单取消'),
          h('li', '批量录入：为入库单批量添加明细信息，支持Excel导入'),
          h('li', '查看详情：查看入库单的完整信息和明细列表'),
        ]),
      ]),
      h('div', [
        h('h4', { class: 'font-medium mb-2' }, '状态说明：'),
        h('ul', { class: 'list-disc list-inside space-y-1 text-sm' }, [
          h('li', '待确认：新创建的入库单，可以编辑、确认、取消、批量录入'),
          h('li', '已确认：已确认的入库单，只能查看详情'),
          h('li', '已完成：已完成的入库单，只能查看详情'),
          h('li', '已取消：已取消的入库单，只能查看详情'),
        ]),
      ]),
      h('div', [
        h('h4', { class: 'font-medium mb-2' }, '操作提示：'),
        h('ul', { class: 'list-disc list-inside space-y-1 text-sm' }, [
          h('li', '批量录入支持Excel文件导入，请先下载模板'),
          h('li', '删除操作仅对待确认状态的入库单有效'),
          h('li', '确认和取消操作不可逆，请谨慎操作'),
        ]),
      ]),
    ]),
  });
}
</script>

<template>
  <Page auto-content-height>
    <FormModal />
    <DetailModal />
    <ConfirmModalComponent />
    <CancelModalComponent />
    <BatchLinesModalComponent />
    
    <Grid>
      <template #toolbar-buttons>
        <Button
          v-show="showDeleteButton"
          danger
          type="primary"
          @click="handleBatchDelete"
        >
          {{ $t('common.delete') }}
        </Button>
      </template>

      <template #toolbar-tools>
        <Space>
          <Button type="primary" @click="openFormModal">
            新增入库单
          </Button>
          <Button type="default" @click="showHelp">
            使用说明
          </Button>
        </Space>
      </template>
    </Grid>
  </Page>
</template>
