import type { VxeGridProps } from '#/adapter/vxe-table';
import { h } from 'vue';
import { type VbenFormProps } from '@vben/common-ui';
import { Button, Tag, Tooltip } from 'ant-design-vue';
import { z } from '#/adapter/form';
import { getChannelList } from '#/api/iot/card';
import dayjs from 'dayjs';
import { router } from '#/router';
import { VbenIcon } from '../../../../../../../packages/@core/ui-kit/shadcn-ui/src/components';

export const tableColumns: VxeGridProps = {
  columns: [
    {
      title: '创建时间',
      field: 'createdAt',
      width: 180,
      formatter: ({ cellValue }) => {
        return dayjs(cellValue * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '文件名称',
      field: 'fileName',
      slots: {
        default: ({ row }) => {
          try {
            const extData = JSON.parse(row.extData || '{}');
            const fileName = extData.file.name || '未知文件名';
            return [
              h(
                'div',
                {
                  class: 'flex items-center justify-center gap-2',
                },
                [
                  fileName,
                  h(Tooltip, { title: '下载文件' }, () =>
                    h(VbenIcon, {
                      class: 'cursor-pointer',
                      icon: 'material-symbols:download',
                      size: 16,
                      onClick: () => {
                        window.open(extData.file.url, '_blank');
                      },
                    }),
                  ),
                ],
              ),
            ];
          } catch (error) {
            return '文件名解析错误';
          }
        },
      },
    },
    {
      title: '单卡成本',
      field: 'costPrice',
      width: 140,
      slots: {
        default: ({ row }) => {
          try {
            const extData = JSON.parse(row.extData || '{}');
            return (
              Number(extData.cost).toLocaleString('zh-CN', {
                style: 'currency',
                currency: 'CNY',
              }) || '未知单卡成本'
            );
          } catch (error) {
            return '文件名解析错误';
          }
        },
      },
    },
    {
      title: '总记录数',
      field: 'totalRecords',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(
            Button,
            {
              type: 'link',
              onClick: () => {
                router.push({
                  path: '/iot/task/log',
                  query: {
                    taskId: row.id,
                    status: 0,
                  },
                });
              },
            },
            () => row.totalRecords,
          ),
      },
    },
    {
      title: '成功记录',
      field: 'successRecords',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(
            Button,
            {
              type: 'link',
              style: { color: 'green' },
              onClick: () => {
                router.push({
                  path: '/iot/task/log',
                  query: {
                    taskId: row.id,
                    status: 1,
                  },
                });
              },
            },
            () => row.successRecords,
          ),
      },
    },
    {
      title: '失败记录',
      field: 'failedRecords',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(
            Button,
            {
              type: 'link',
              style: { color: 'red' },
              onClick: () => {
                router.push({
                  path: '/iot/task/log',
                  query: {
                    taskId: row.id,
                    status: 2,
                  },
                });
              },
            },
            () => row.failedRecords,
          ),
      },
    },
    {
      title: '操作人',
      field: 'operatorId',
      width: 220,
    },
    {
      title: '状态',
      field: 'status',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(
            Tag,
            {
              color:
                row.status === 1
                  ? 'default'
                  : row.status === 2
                    ? 'processing'
                    : row.status === 3
                      ? 'success'
                      : 'error',
            },
            () => {
              switch (row.status) {
                case 1:
                  return '待处理';
                case 2:
                  return '处理中';
                case 3:
                  return '已完成';
                case 4:
                  return '失败';
                default:
                  return '未知';
              }
            },
          ),
      },
    },
    {
      title: '文字状态',
      field: 'errorMessage',
      align: 'center',
      width: 200,
      slots: {
        default: ({ row }) =>
          h(
            Tooltip,
            {
              placement: 'top',
              title: row.errorMessage,
            },
            () =>
              h(
                'div',
                {
                  style: {
                    width: '200px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  },
                },
                row.errorMessage,
              ),
          ),
      },
    },
    {
      title: '操作',
      field: 'action',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(
            Button,
            {
              type: 'link',
              onClick: () => {
                router.push({
                  path: '/iot/task/log',
                  query: {
                    taskId: row.id,
                    status: 0,
                  },
                });
              },
            },
            () => '查看详情',
          ),
      },
    },
  ],
};

export const searchFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      rules: 'required',
      componentProps: {
        options: [
          { label: '全部', value: 0 },
          { label: '待处理', value: 1 },
          { label: '处理中', value: 2 },
          { label: '已完成', value: 3 },
          { label: '失败', value: 4 },
        ],
      },
      defaultValue: 0,
    },
  ],
};

export const importFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'operator',
      label: '卡通道',
      component: 'ApiSelect',
      componentProps: {
        api: getChannelList,
        params: {
          page: {
            page: 1,
            pageSize: 1000,
          },
        },
        labelField: 'name',
        valueField: 'id',
      },
      rules: 'required',
    },
    {
      fieldName: 'costPrice',
      label: '卡片费单价',
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入卡片成本单价',
      },
      rules: z
        .number()
        .min(0, '卡片费单价不能小于0')
        .and(z.number().max(10000, '卡片费单价不能大于10000')),
      defaultValue: 0,
      suffix: '¥',
      help: '卡片费单价为卡片成本单价，不包含手续费，⚠️注意，此操作会将流水记录到对应的卡商，如需修改请在批量操作里进行',
    },
    {
      fieldName: 'tags',
      label: '标签',
      component: 'Select',
      componentProps: {
        mode: 'tags',
        placeholder: '请输入标签，可多选',
        tokenSeparators: [',', '，', ' '],
      },
      help: '输入标签后按回车或逗号分隔，可添加多个标签，可以用于批次分类，方便批量操作',
    },
    {
      fieldName: 'file',
      label: '导入文件',
      component: 'UploadFile',
      componentProps: {
        maxCount: 1,
        maxSize: 10 * 1024 * 1024,
        multiple: false,
        accept: ['xlsx', 'xls', 'csv'],
      },
      rules: z
        .object({
          url: z.string(),
          name: z.string(),
        })
        .refine(
          (val) => {
            return val && val.url;
          },
          {
            message: '请选择导入文件',
          },
        ),
    },
  ],
};
