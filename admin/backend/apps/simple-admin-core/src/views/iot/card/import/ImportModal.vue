<template>
  <Modal
    title="导入卡板"
  >
    <Form />
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { message } from 'ant-design-vue';
import { importFormSchemas } from './schemas';
import { importCard } from '#/api/iot/card';

defineOptions({
  name: 'CardImportModal',
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  wrapperClass: 'w-full',
  schema: [...(importFormSchemas.schema as any)],
  showDefaultActions: false,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-2/6',
  }
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    const values = await formApi.getValues();
    const validationResult = await formApi.validate();
    if (validationResult.valid) {
      // 调用导入接口
      const res = await importCard({
        channel: values.operator,
        cost: values.costPrice,
        file: values.file,
        tags: values.tags,
        cover: values.cover,
      });
      if(res.code === 0){
        message.success('导入成功');
      }else{
        message.error(res.msg);
      }
      modalApi.close();
    }
  },
});

defineExpose(modalApi);
</script>
