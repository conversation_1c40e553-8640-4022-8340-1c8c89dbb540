import type { VxeGridProps } from '#/adapter/vxe-table';
import { h } from 'vue';
import { type VbenFormProps } from '@vben/common-ui';
import { Button, Tag, Tooltip } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { CardInfo } from '#/api/iot/model/cardModel';
import { router } from '#/router';

export const tableColumns: VxeGridProps<CardInfo> = {
  columns: [
    {
      title: '卡号',
      field: 'iccid',
      width: 200,
    },
    {
      title: 'MSISDN',
      field: 'msisdn',
      width: 150,
    },
    {
      title: 'IMSI',
      field: 'imsi',
      width: 150,
    },
    {
      title: '状态',
      field: 'status',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(
            Tag,
            {
              color:
                row.status === 1
                  ? 'blue'
                  : row.status === 2
                    ? 'green'
                    : row.status === 3
                      ? 'success'
                      : 'default',
            },
            () => {
              switch (row.status) {
                case 1:
                  return '可测试';
                case 2:
                  return '已激活';
                case 3:
                  return '未激活';
                default:
                  return '未知';
              }
            },
          ),
      },
    },
    {
      title: '通道',
      field: 'channel',
      width: 150,
      slots: {
        default: ({ row }) => row.channel?.name || '-',
      },
    },
    {
      title: '所属设备',
      field: 'device',
      width: 160,
      slots: {
        default: ({ row }) => 
          row.device 
            ? h(
                Button, 
                {
                  type: 'link',
                  style: { color: 'green' },
                  onClick: () => {
                    router.push({
                      path: '/iot/device/detail',
                      query: {
                        id: row.device?.id
                      }
                    });
                  }
                }, 
                () => row.device?.deviceNo
              )
            : h(
                Tag, 
                { color: 'red' }, 
                () => '暂未分配设备'
              ),
      },
    },
    {
      title: '标签',
      field: 'tags',
      width: 200,
      slots: {
        default: ({ row }) =>
          h(
            'div',
            {
              class: 'flex flex-wrap gap-1',
            },
            row.tags?.map((tag) =>
              h(
                Tag,
                {
                  color: 'blue',
                },
                () => tag,
              ),
            ),
          ),
      },
    },
    {
      title: '表导入时间',
      field: 'addedDate',
      titleSuffix: {
        icon: 'vxe-icon-question-circle',
        content: 'Excel 里面的导入时间',
      },
      width: 180,
      formatter: ({ cellValue }) => {
        return dayjs(cellValue * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '表创建时间',
      field: 'createdAt',
      width: 180,
      titleSuffix: {
        icon: 'vxe-icon-question-circle',
        content: 'Excel 导入到本系统的时间',
      },
      formatter: ({ cellValue }) => {
        return dayjs(cellValue * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
  ],
};

export const searchFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'iccid',
      label: '卡号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入卡号',
      },
    },
    {
      fieldName: 'device_no',
      label: '设备编号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入设备编号',
      },
      help:'可以通过设备编号查询指定设备下的所有卡号'
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        options: [
          { label: '全部', value: 0 },
          { label: '可测试', value: 1 },
          { label: '已激活', value: 2 },
          { label: '未激活', value: 3 },
        ],
        placeholder: '请选择状态',
      },
    },
    {
      fieldName: 'tags',
      label: '标签',
      component: 'Select',
      componentProps: {
        mode: 'tags',
        placeholder: '请输入标签，可多选',
        tokenSeparators: [',', '，', ' '],
      },
      help: '输入标签后按回车或逗号分隔，可添加多个标签',
    },
    {
      fieldName: 'addedDateRange',
      label: '表导入时间',
      component: 'RangePicker',
      help: 'Excel 里面的导入时间',
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
      },
    },
    {
      fieldName: 'createdDateRange',
      label: '表创建时间',
      help: 'Excel 导入到本系统的时间',
      component: 'RangePicker',
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
      },
    },
  ],
};
