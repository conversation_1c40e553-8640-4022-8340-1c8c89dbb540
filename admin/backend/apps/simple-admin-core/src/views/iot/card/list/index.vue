<template>
  <Page auto-content-height>
    <Grid>
      <template #device="{ row }">
        <CopyWrapper type="icon" position="right" copy-text="DEV-2024-001" success-message="设备编号已复制">
          <a style="color: green; cursor: pointer">
            {{ row.device?.deviceNo }}
          </a>
        </CopyWrapper>
      </template>

    </Grid>
  </Page>
</template>

<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import { CopyWrapper } from '#/components/common';
import { h, ref, onMounted } from 'vue';
import { Page, type VbenFormProps } from '@vben/common-ui';
import { Button } from 'ant-design-vue';
import { ReloadOutlined } from '@ant-design/icons-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { searchFormSchemas, tableColumns } from './schemas';
import { getCardList } from '#/api/iot/card';
import type { CardInfo } from '#/api/iot/model/cardModel';
import { useRoute } from 'vue-router';

defineOptions({
  name: 'CardList',
});

const route = useRoute();

const formOptions: VbenFormProps = {
  schema: [...(searchFormSchemas.schema as any)],
  collapsed: false,
  showCollapseButton: true,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
};

const gridOptions: VxeGridProps<CardInfo> = {
  columns: tableColumns.columns,
  height: 'auto',
  keepSource: true,
  stripe: true,
  toolbarConfig: {
    custom: true,
    export: false,
    import: false,
    refresh: true,
    zoom: true,
  },
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formData) => {
        const res = await getCardList({
          page: {
            page: page.currentPage,
            pageSize: page.pageSize,
          },
          ...formData,
        });
        return res.data;
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});

// 处理路由参数
onMounted(() => {
  const { iccid, status } = route.query;
  if (iccid || status) {
    const formValues = {
      iccid: iccid ? String(iccid) : undefined,
      status: status ? Number(status) : undefined,
    };
    // 设置表单值
    Object.entries(formValues).forEach(([key, value]) => {
      if (value !== undefined) {
        gridApi.formApi.setFieldValue(key, value);
      }
    });
    gridApi.query();
  }
});

function handleRefresh() {
  gridApi.value.reload();
}
</script>
