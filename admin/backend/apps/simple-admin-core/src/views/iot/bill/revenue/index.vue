<template>
  <Page>
    <Card class="mb-4">
      <template #title>收入统计</template>
      <template #extra>
        <Space>
          <DatePicker.RangePicker
            v-model:value="dateRange"
            :show-time="{ format: 'HH:mm' }"
            format="YYYY-MM-DD HH:mm"
            @change="handleDateChange"
          />
          <Button type="primary" @click="handleSearch">查询</Button>
          <Button @click="handleReset">重置</Button>
        </Space>
      </template>
      <div class="mb-4">
        <Row :gutter="16">
          <Col :span="6">
            <Statistic
              title="总收入"
              :value="statistics.totalAmount"
              :precision="2"
              prefix="￥"
            />
          </Col>
          <Col :span="6">
            <Statistic
              title="今日收入"
              :value="statistics.todayAmount"
              :precision="2"
              prefix="￥"
            />
          </Col>
          <Col :span="6">
            <Statistic
              title="本月收入"
              :value="statistics.monthAmount"
              :precision="2"
              prefix="￥"
            />
          </Col>
          <Col :span="6">
            <Statistic
              title="交易笔数"
              :value="statistics.transactionCount"
            />
          </Col>
        </Row>
      </div>
    </Card>

    <Card>
      <template #title>收入明细</template>
      <Grid>
        <template #toolbar-tools>
          <Button type="primary" @click="handleExport">
            <template #icon><DownloadOutlined /></template>
            导出
          </Button>
        </template>
      </Grid>
    </Card>
  </Page>
</template>

<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import { h, ref, onMounted } from 'vue';
import { Card, Row, Col, Statistic, Space, Button, DatePicker } from 'ant-design-vue';
import { Page } from '@vben/common-ui';
import { DownloadOutlined } from '@ant-design/icons-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getRevenueList, getRevenueStatistics } from '#/api/iot/revenue';
import type { Dayjs } from 'dayjs';
import { tableColumns } from './schemas';
import { message } from 'ant-design-vue';

defineOptions({
  name: 'RevenueBill',
});

const [messageApi, contextHolder] = message.useMessage();

// 日期范围
const dateRange = ref<[Dayjs, Dayjs] | undefined>();

// 统计数据
const statistics = ref({
  totalAmount: 0,
  todayAmount: 0,
  monthAmount: 0,
  transactionCount: 0,
});

const gridOptions: VxeGridProps = {
  columns: tableColumns.columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    pageSize: 10,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        const params = {
          page: page.currentPage,
          pageSize: page.pageSize,
          startTime: dateRange.value?.[0]?.format('YYYY-MM-DD HH:mm:ss'),
          endTime: dateRange.value?.[1]?.format('YYYY-MM-DD HH:mm:ss'),
        };
        const res = await getRevenueList(params);
        return {
          items: res.data.data || [],
          total: res.data.total || 0,
        };
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const params = {
      startTime: dateRange.value?.[0]?.format('YYYY-MM-DD HH:mm:ss'),
      endTime: dateRange.value?.[1]?.format('YYYY-MM-DD HH:mm:ss'),
    };
    const res = await getRevenueStatistics(params);
    if (res.data) {
      statistics.value = res.data;
    }
  } catch (error) {
    messageApi.error('获取统计数据失败');
  }
};

// 日期变化
const handleDateChange = () => {
  gridApi.reload();
  fetchStatistics();
};

// 搜索
const handleSearch = () => {
  gridApi.reload();
  fetchStatistics();
};

// 重置
const handleReset = () => {
  dateRange.value = undefined;
  handleSearch();
};

// 导出
const handleExport = () => {
  // TODO: 实现导出功能
  messageApi.info('导出功能开发中');
};

onMounted(() => {
  fetchStatistics();
});
</script> 
