import type { VxeGridProps } from '#/adapter/vxe-table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

export const tableColumns: VxeGridProps = {
  columns: [
    {
      type: 'checkbox',
      width: 60,
    },
    {
      title: 'ID',
      field: 'id',
      width: 80,
    },
    {
      title: '成本类型',
      field: 'costTypeName',
      width: 120,
    },
    {
      title: '金额',
      field: 'amount',
      width: 120,
      slots: {
        default: ({ row }) => h('span', { class: 'text-red-600' }, `￥${row.amount}`),
      },
    },
    {
      title: '关联订单',
      field: 'relatedOrderId',
      width: 120,
    },
    {
      title: '关联实体',
      field: 'relatedEntityId',
      width: 120,
    },
    {
      title: '描述',
      field: 'description',
    },
    {
      title: '创建时间',
      field: 'createdAt',
      width: 180,
    },
    {
      title: '状态',
      field: 'isRefunded',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(Tag, {
            color: row.isRefunded ? 'error' : 'success',
          }, () => row.isRefunded ? '已退款' : '正常'),
      },
    },
  ],
}; 