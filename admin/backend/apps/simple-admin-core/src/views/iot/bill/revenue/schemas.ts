import type { VxeGridProps } from '#/adapter/vxe-table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

export const tableColumns: VxeGridProps = {
  columns: [
    {
      title: 'ID',
      field: 'id',
      width: 80,
    },
    {
      title: '收入类型',
      field: 'revenueTypeName',
      width: 120,
    },
    {
      title: '金额',
      field: 'amount',
      width: 120,
      slots: {
        default: ({ row }) => {
          return h('span', { class: 'text-green-600' }, `￥${row.amount}`);
        },
      },
    },
    {
      title: '关联订单',
      field: 'relatedOrderId',
      width: 120,
    },
    {
      title: '关联实体',
      field: 'relatedEntity',
      width: 120,
    },
    {
      title: '描述',
      field: 'description',
    },
    {
      title: '创建时间',
      field: 'createdAt',
      width: 180,
    },
    {
      title: '状态',
      field: 'status',
      width: 100,
      slots: {
        default: ({ row }) => {
          const statusMap = {
            pending: { text: '待处理', color: 'warning' },
            completed: { text: '已完成', color: 'success' },
            failed: { text: '失败', color: 'error' },
          };
          const status = statusMap[row.status as keyof typeof statusMap] || { text: '未知', color: 'default' };
          return h(Tag, { color: status.color }, () => status.text);
        },
      },
    },
  ],
}; 