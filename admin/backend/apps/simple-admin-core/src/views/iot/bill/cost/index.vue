<template>
  <Page auto-content-height>
    <Card class="mb-4">
      <template #title>成本统计</template>
      <template #extra>
        <Space>
          <DatePicker.RangePicker
            v-model:value="dateRange"
            :show-time="{ format: 'HH:mm' }"
            format="YYYY-MM-DD HH:mm"
            @change="handleDateChange"
          />
          <Select
            v-model:value="selectedCostType"
            style="width: 200px"
            placeholder="选择成本类型"
            allowClear
            @change="handleSearch"
          >
            <SelectOption
              v-for="item in costTypes"
              :key="item.id"
              :value="item.id"
            >
              {{ item.name }}
            </SelectOption>
          </Select>
          <Button type="primary" @click="handleSearch">查询</Button>
          <Button @click="handleReset">重置</Button>
        </Space>
      </template>
      <div class="mb-4">
        <Row :gutter="16">
          <Col :span="6">
            <Statistic
              title="总成本"
              :value="statistics.totalAmount"
              :precision="2"
              prefix="￥"
            />
          </Col>
          <Col :span="6">
            <Statistic
              title="今日成本"
              :value="statistics.todayAmount"
              :precision="2"
              prefix="￥"
            />
          </Col>
          <Col :span="6">
            <Statistic
              title="本月成本"
              :value="statistics.monthAmount"
              :precision="2"
              prefix="￥"
            />
          </Col>
          <Col :span="6">
            <Statistic
              title="交易笔数"
              :value="statistics.transactionCount"
            />
          </Col>
        </Row>
      </div>
    </Card>

    <Card class="mb-4">
      <template #title>成本类型分布</template>
      <EchartsUI ref="costTypeChartRef" height="400px" />
    </Card>

    <Card>
      <template #title>成本明细</template>
      <Grid>
        <template #toolbar-tools>
          <Button type="primary" @click="handleExport">
            <template #icon><DownloadOutlined /></template>
            导出
          </Button>
        </template>
      </Grid>
    </Card>
  </Page>
</template>

<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import { h, ref, onMounted } from 'vue';
import { Card, Row, Col, Statistic, Space, Button, DatePicker, Select } from 'ant-design-vue';
import { Page } from '@vben/common-ui';
import { DownloadOutlined } from '@ant-design/icons-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { message,SelectOption } from 'ant-design-vue';
import { getCostList, getCostStatistics, getCostTypes, type CostType } from '#/api/iot/cost';
import type { Dayjs } from 'dayjs';
import { tableColumns } from './schemas';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import type { EchartsUIType } from '@vben/plugins/echarts';
import type { CostListReq } from '#/api/iot/cost';

const [messageApi, contextHolder] = message.useMessage();

defineOptions({
  name: 'CostBill',
});

// 日期范围
const dateRange = ref<[Dayjs, Dayjs]>();

// 成本类型
const costTypes = ref<CostType[]>([]);
const selectedCostType = ref<number | undefined>();

// 统计数据
const statistics = ref({
  totalAmount: 0,
  todayAmount: 0,
  monthAmount: 0,
  transactionCount: 0,
});

const gridOptions: VxeGridProps = {
  columns: tableColumns.columns,
  height: '500px',
  keepSource: true,
  pagerConfig: {
    pageSize: 10,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        const params = {
          page: page.currentPage,
          pageSize: page.pageSize,
          costTypeId: selectedCostType.value,
          startTime: dateRange.value?.[0]?.format('YYYY-MM-DD HH:mm:ss'),
          endTime: dateRange.value?.[1]?.format('YYYY-MM-DD HH:mm:ss'),
        };
        const res = await getCostList(params);
        return {
          items: res.data.data || [],
          total: res.data.total || 0,
        };
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

// 成本类型分布图表
const costTypeChartRef = ref<EchartsUIType>();
const { renderEcharts: renderCostTypeChart } = useEcharts(costTypeChartRef);

// 获取成本类型
const fetchCostTypes = async () => {
  try {
    // const res = await getCostTypes();
    // if (res.data) {
    //   costTypes.value = res.data;
    // }
    // 模拟数据
    const mockCostTypes = [
      { id: 1, name: '设备采购', description: '购买物联网设备及相关硬件' },
      { id: 2, name: '网络费用', description: '物联网卡流量费用及网络服务费' },
      { id: 3, name: '人工成本', description: '运维人员工资及外包服务费' },
      { id: 4, name: '服务器费用', description: '云服务器租用及维护费用' },
      { id: 5, name: '软件授权', description: '软件使用授权及订阅费用' },
      { id: 6, name: '营销费用', description: '市场推广及广告费用' },
      { id: 7, name: '办公费用', description: '办公场地及日常运营费用' },
      { id: 8, name: '其他支出', description: '其他未分类的支出' }
    ];
    costTypes.value = mockCostTypes;
  } catch (error) {
    messageApi.error('获取成本类型失败');
  }
};

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const params = {
      costTypeId: selectedCostType.value,
      startTime: dateRange.value?.[0]?.format('YYYY-MM-DD HH:mm:ss'),
      endTime: dateRange.value?.[1]?.format('YYYY-MM-DD HH:mm:ss'),
    };
    const res = await getCostStatistics(params);
    if (res.data) {
      statistics.value = res.data;
    }
  } catch (error) {
    messageApi.error('获取统计数据失败');
  }
};

// 获取成本类型分布数据
const fetchCostTypeDistribution = async () => {
  try {
    const params: CostListReq = {
      page: 1,
      pageSize: 9999,
      costTypeId: selectedCostType.value,
      startTime: dateRange.value?.[0]?.format('YYYY-MM-DD HH:mm:ss'),
      endTime: dateRange.value?.[1]?.format('YYYY-MM-DD HH:mm:ss'),
    };
    // 模拟数据
    const mockData = [
      { costTypeId: 1, amount: 150000 },
      { costTypeId: 2, amount: 80000 },
      { costTypeId: 3, amount: 120000 },
      { costTypeId: 4, amount: 50000 },
      { costTypeId: 5, amount: 30000 },
      { costTypeId: 6, amount: 40000 },
      { costTypeId: 7, amount: 25000 },
      { costTypeId: 8, amount: 15000 }
    ];

    // 按成本类型分组统计
    const costTypeMap = new Map<number, number>();
    mockData.forEach((item) => {
      const typeId = item.costTypeId;
      const amount = Number(item.amount);
      costTypeMap.set(typeId, (costTypeMap.get(typeId) || 0) + amount);
    });

    // 转换为饼图数据格式
    const pieData = Array.from(costTypeMap.entries()).map(([typeId, amount]) => {
      const type = costTypes.value.find(t => t.id === typeId);
      return {
        name: type?.name || '未知类型',
        value: amount
      };
    });

    // 渲染饼图
    renderCostTypeChart({
      tooltip: {
        trigger: 'item',
        formatter: '{b}: ￥{c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        bottom: 'bottom'
      },
      series: [
        {
          name: '成本类型分布',
          type: 'pie',
          radius: '50%',
          data: pieData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    });
  } catch (error) {
    messageApi.error('获取成本类型分布数据失败');
  }
};

// 日期变化
const handleDateChange = () => {
  gridApi.reload();
  fetchStatistics();
};

// 搜索
const handleSearch = () => {
  gridApi.reload();
  fetchStatistics();
  fetchCostTypeDistribution();
};

// 重置
const handleReset = () => {
  dateRange.value = undefined;
  selectedCostType.value = undefined;
  handleSearch();
};

// 导出
const handleExport = () => {
  // TODO: 实现导出功能
  messageApi.info('导出功能开发中');
};

onMounted(() => {
  fetchCostTypes();
  fetchStatistics();
  fetchCostTypeDistribution();
});
</script> 
