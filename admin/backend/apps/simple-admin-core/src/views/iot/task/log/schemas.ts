import type { VxeGridProps } from '#/adapter/vxe-table';
import { h } from 'vue';
import { type VbenFormProps } from '@vben/common-ui';
import { Tag, Tooltip } from 'ant-design-vue';
import { z } from '#/adapter/form';
import dayjs from 'dayjs';
import type { TaskLogInfo } from '#/api/iot/model/taskModel';

export const tableColumns: VxeGridProps<TaskLogInfo> = {
  columns: [
    {
      title: '创建时间',
      field: 'createdAt',
      width: 180,
      formatter: ({ cellValue }) => {
        return dayjs(cellValue * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '任务ID',
      field: 'taskId',
      width: 100,
    },
    {
      title: '状态',
      field: 'status',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(Tag, {
            color: row.status === 1 ? 'success' : row.status === 2 ? 'error' : 'default',
          }, () => {
            switch (row.status) {
              case 1:
                return '成功';
              case 2:
                return '失败';
              default:
                return '未知';
            }
          }),
      },
    },
    {
      title: '导入项',
      field: 'recordText',
      width:300,
      slots: {
        default: ({ row }) => 
          h(Tooltip, { 
            placement: 'top', 
            title: row.recordText 
          }, () => 
            h('div', {
              style: {
                class: 'w-full flex items-center justify-center',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }
            }, row.recordText)
          )
      },
    },
    {
      title: '错误信息',
      field: 'errorMessage',
      slots: {
        default: ({ row }) => 
          h(Tooltip, { 
            placement: 'top', 
            title: row.errorText 
          }, () => 
            h('div', {
              class: 'w-full flex items-center justify-center',
              style: {
                color: 'red',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }
            }, row.errorText)
          )
      },
    },
  ],
};

export const searchFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'taskId',
      label: '任务ID',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入任务ID',
        disabled: true,
      },
      help: '任务ID为必填项,无法手动输入',
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        options: [
          { label: '全部', value: 0 },
          { label: '成功', value: 1 },
          { label: '失败', value: 2 },
        ],
        placeholder: '请选择状态',
      },
    }
  ],
}; 
