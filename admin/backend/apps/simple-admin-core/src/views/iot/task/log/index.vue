<template>
  <Page auto-content-height>
    <Grid>
    </Grid>
  </Page>
</template>

<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import { onMounted, ref } from 'vue';
import { Page, type VbenFormProps } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { searchFormSchemas, tableColumns } from './schemas';
import { getTaskLog } from '#/api/iot/task';
import { useRoute } from 'vue-router';
import type { TaskLogInfo } from '#/api/iot/model/taskModel';

defineOptions({
  name: 'TaskLogManagement',
});

const route = useRoute();

// 处理路由参数
onMounted(() => {
  const { taskId, status } = route.query;
  if (taskId || status) {
    const formValues = {
      taskId: taskId ? Number(taskId) : undefined,
      status: status ? Number(status) : undefined,
    };
    // 设置表单值
    Object.entries(formValues).forEach(([key, value]) => {
      if (value !== undefined) {
        gridApi.formApi.setFieldValue(key, Number(value));
      }
    });
    gridApi.query();
  }
});

const formOptions: VbenFormProps = {
  collapsed: false,
  schema: [...(searchFormSchemas.schema as any)],
  showCollapseButton: true,
  submitOnEnter: false,
  resetButtonOptions: {
    show: false,
  },
};

const gridOptions: VxeGridProps<TaskLogInfo> = {
  columns: tableColumns.columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  sortConfig: {
    multiple: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {

        // 如果formValues.taskId为空，则不查询
        if (!formValues.taskId) {
          return {
            data: [],
            total: 0,
          };
        }

        const res = await getTaskLog({
          page: {
            page: page.currentPage,
            pageSize: page.pageSize,
          },
          ...formValues,
        });
        return res.data;
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});
</script> 
