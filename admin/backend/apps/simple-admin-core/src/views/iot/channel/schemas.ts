import type { VxeGridProps } from '#/adapter/vxe-table';
import { h } from 'vue';
import { type VbenFormProps } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { Button, Tag } from 'ant-design-vue';
import { z } from '#/adapter/form';
import dayjs from 'dayjs';

export const tableColumns: VxeGridProps = {
  columns: [
    {
      title: '通道名称',
      field: 'name',
      width: 150,
    },
    {
      title: '所属地区',
      field: 'region',
      width: 120,
    },
    {
      title: '通道类型',
      field: 'channelType',
      width: 120,
      slots: {
        default: ({ row }) => h(Tag, {}, () => row.channelType),
      },
    },
    {
      title: '所属运营商',
      field: 'operator',
      width: 120,
      slots: {
        default: ({ row }) => h(Tag, {}, () => row.operator),
      },
    },
    {
      title: '联系人',
      field: 'contactPerson',
      width: 100,
    },
    {
      title: '联系电话',
      field: 'contactPhone',
      width: 120,
    },
    {
      title: '通道余额',
      field: 'balance',
      width: 120,
      slots: {
        default: ({ row }) => `¥ ${Number(row.balance).toFixed(2)}`,
      },
    },
    {
      title: '实名认证',
      field: 'realNameRequired',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(Tag, {
            color: row.realNameRequired ? 'success' : 'default',
          }, () => row.realNameRequired ? '需要' : '不需要'),
      },
    },
    {
      title: '扩展数据',
      field: 'extData',
      slots: {
        default: ({ row }) => JSON.stringify(row.extData),
      },
    },
    {
      title: '创建时间',
      field: 'createdAt',
      width: 180,
      slots: {
        // 格式化时间,将时间戳秒格式化
        default: ({ row }) => dayjs(row.createdAt * 1000).format('YYYY-MM-DD HH:mm:ss'),
      },
    },
    {
      field: 'action',
      fixed: 'right',
      slots: { default: 'action' },
      title: '操作',
      width: 120,
    },
  ],
};

export const channelFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'name',
      label: '通道名称',
      component: 'Input',
      componentProps: {
        placeholder: '请输入通道名称',
      },
      rules: 'required',
    },
    {
      fieldName: 'region',
      label: '所属地区',
      component: 'Input',
      componentProps: {
        placeholder: '请输入所属地区',
      },
    },
    {
      fieldName: 'channelType',
      label: '通道类型',
      component: 'Select',
      componentProps: {
        options: [
          { label: '运营商', value: '运营商' },
          { label: '虚拟运营商', value: '虚拟运营商' },
          { label: '国际通道', value: '国际通道' },
        ],
      },
      rules: 'required',
    },
    {
      fieldName: 'operator',
      label: '所属运营商',
      component: 'Select',
      componentProps: {
        options: [
          { label: '中国移动', value: 'CMCC' },
          { label: '中国联通', value: 'CUCC' },
          { label: '中国电信', value: 'CTCC' },
        ],
      },
      rules: 'required',
    },
    {
      fieldName: 'contactPerson',
      label: '联系人',
      component: 'Input',
      componentProps: {
        placeholder: '请输入联系人姓名',
      },
    },
    {
      fieldName: 'contactPhone',
      label: '联系电话',
      component: 'Input',
      componentProps: {
        placeholder: '请输入联系电话',
      },
    },
    {
      fieldName: 'realNameRequired',
      label: '实名认证',
      component: 'Checkbox',
      defaultValue: true,
      renderComponentContent: () => {
        return {
          default: () => ['是否需要实名认证'],
        };
      },
    },
    {
      fieldName: 'realNameUrl',
      label: '实名认证地址',
      component: 'Input',
      componentProps: {
        placeholder: '请输入实名认证地址',
      },
    },
    {
      fieldName: 'balance',
      label: '通道余额',
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入通道余额',
      },
      defaultValue: 0,
      suffix: '¥',
      help: '对于编辑时，余额将无法手动输入',
    },
    {
      fieldName: 'extData',
      label: '扩展数据',
      component: 'CodeEditor',
      componentProps: {
        mode: 'json',
      },
      defaultValue: '{}',
      help: '请输入扩展数据,json格式',
    }
  ],
}; 

export const searchFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'name',
      label: '通道名称',
      component: 'Input',
    },
  ],
};
