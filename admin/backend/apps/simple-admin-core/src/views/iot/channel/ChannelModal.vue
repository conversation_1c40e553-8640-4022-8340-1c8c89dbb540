<template>
  <Modal
    :title="modalTitle"
  >
    <Form />
  </Modal>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { message } from 'ant-design-vue';
import { channelFormSchemas } from './schemas';
import { createChannel } from '#/api/iot/card';
import type { ChannelCreateReq } from '#/api/iot/model/cardModel';

defineOptions({
  name: 'ChannelModal',
});

const modalData = ref<{
  type: 'add' | 'edit';
  record?: any;
}>({
  type: 'add',
});

const modalTitle = computed(() => modalData.value.type === 'add' ? '新增通道' : '编辑通道');

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  wrapperClass: 'grid-cols-1 md:grid-cols-2 gap-4',
  schema: [...(channelFormSchemas.schema as any)],
  showDefaultActions: false,
  commonConfig: {
    componentProps: {
      class: 'col-span-2 md:col-span-1 w-full',
    },
    labelClass: 'w-4/6',
  }
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    const validationResult = await formApi.validate();
    if (validationResult.valid) {
      try {
        const formData = await formApi.getValues();
        
        // 转换表单数据为 API 所需格式
        const apiData: ChannelCreateReq = {
          name: formData.name,
          channelType: formData.channelType,
          operator: formData.operator,
          balance: Number(formData.balance),
          realNameRequired: Boolean(formData.realNameRequired),
          region: formData.region,
          contactPerson: formData.contactPerson,
          contactPhone: formData.contactPhone,
          realNameUrl: formData.realNameUrl,
          extData: typeof formData.extData === 'string' ? formData.extData : JSON.stringify(formData.extData),
          id: modalData.value.record?.id,
        };

        const res = await createChannel(apiData);
        if(res.code === 0){
          message.success(modalData.value.type === 'add' ? '新增成功' : '编辑成功');
        }else{
          message.error(res.msg);
        }
        modalApi.close();
      } catch (error) {
        console.error('提交失败:', error);
        message.error('操作失败');
      }
    }
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData();
      modalData.value = {
        type: data.type,
        record: data.record,
      };
      if (data.type === 'edit' && data.record) {
        // 对于编辑时，balance 将无法手动输入，需要设置为只读
        formApi.updateSchema([
        {
          componentProps: {
            disabled: true,
          },
          fieldName: 'balance',
        },
      ]);
        formApi.setValues(data.record);
      }else{
        formApi.updateSchema([
        {
          componentProps: {
            disabled: false,
          },
          fieldName: 'balance',
        },
      ]);
      }
    }
  },
});

defineExpose(modalApi);
</script> 
