<template>
  <Page auto-content-height>
    <ChannelModalComponent />
    <Grid>
      <template #toolbar-tools>
        <Button type="primary" @click="handleAdd">
          <template #icon><PlusOutlined /></template>
          新增通道
        </Button>
      </template>
      <!-- 自定义action -->
      <template #action="{ row }">
        <Button type="link" @click="handleEdit(row)">编辑</Button>
      </template>
    </Grid>
  </Page>
</template>

<script lang="ts" setup>
import type {  VxeGridProps } from '#/adapter/vxe-table';
import {  onMounted, ref } from 'vue';
import { Page, useVbenModal, type VbenFormProps } from '@vben/common-ui';
import { Button } from 'ant-design-vue';
import { PlusOutlined, EditOutlined } from '@ant-design/icons-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import ChannelModal from './ChannelModal.vue';
import { searchFormSchemas, tableColumns } from './schemas';
import { getChannelList } from '#/api/iot/card';


defineOptions({
  name: 'ChannelManagement',
});

const [ChannelModalComponent, channelModalApi] = useVbenModal({
  connectedComponent: ChannelModal,
  onBeforeClose: () => {
    gridApi.reload();
    return true;
  },
});

  const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [...(searchFormSchemas
  .schema as any)],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeGridProps = {
  columns: tableColumns.columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  sortConfig: {
    multiple: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page },formValues) => {
        const res = await getChannelList({
          page: {
            page: page.currentPage,
            pageSize: page.pageSize,
          },
          ...formValues,
        });
        return res.data;
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});

function handleAdd() {
  channelModalApi.setData({
    type: 'add',
  });
  channelModalApi.open();
}

function handleEdit(row: any) {
  channelModalApi.setData({
    type: 'edit',
    record: row,
  });
  channelModalApi.open();
}

function handleSuccess() {
  gridApi.reload();
  channelModalApi.close();
}
</script>
