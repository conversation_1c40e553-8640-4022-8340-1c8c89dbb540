import { requestClient } from '#/api/request';
import { type BaseDataResp } from '../model/baseModel';
import {
  type TaskListReq,
  type TaskListResp,
  type TaskLogReq,
  type TaskLogResp,
} from './model/taskModel';

enum Api {
  GetTaskList = '/iot-api/esim-task/list',
  GetTaskLog = '/iot-api/esim-task/log',
}

/**
 * @description: 获取任务列表
 */
export const getTaskList = (params: TaskListReq) => {
  return requestClient.post<BaseDataResp<TaskListResp>>(Api.GetTaskList, params);
};

/**
 * @description: 获取任务日志
 */
export const getTaskLog = (params: TaskLogReq) => {
  return requestClient.post<BaseDataResp<TaskLogResp>>(Api.GetTaskLog, params);
};
