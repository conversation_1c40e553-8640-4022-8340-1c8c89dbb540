import {
  type BaseDataResp,
  type BaseListResp,
  type BaseResp,
  type BaseListReq,
} from '../../model/baseModel';


export interface BaseListInfo {
  total: number;
}

export interface BaseMsgResp extends BaseResp { }

export interface CardChannelInfo {
  id: number;
  name: string;
  operator: string;
  realNameRequired: boolean;
}

export interface CardListDeviceInfo {
  id: number;
  deviceNo: string;
}

export interface CardInfo {
  id: number;
  iccid: string;
  msisdn: string;
  imsi: string;
  createdAt: number;
  addedDate: number;
  tags: string[];
  locked: boolean;
  activatedAt: number;
  status: number;
  channel: CardChannelInfo;
  deviceId: number;
  device?: CardListDeviceInfo;
}

export type CardListResp = BaseListResp<CardInfo>;

export interface CardListReq {
  page: BaseListReq;
  iccid?: string;
  status?: number;
  tags?: string[];
  locked?: boolean;
  channel?: number;
  // 添加时间范围
  //  AddedDateRange []*int64 `json:"addedDateRange,optional"`
  addedDateRange?: [number, number];
  // 创建时间范围
  createdDateRange?: [number, number];
  // device_no
  deviceNo?: string;
}

export interface ImportCardReq {
  file: {
    name: string;
    size: number;
    type: string;
  };
  cost: string;
  channel: number;
  tags?: string[];
  cover: boolean;
}

export interface ChannelCreateReq {
  id?: number;
  name: string;
  region?: string;
  channelType: string;
  operator: string;
  contactPerson?: string;
  contactPhone?: string;
  realNameRequired: boolean;
  realNameUrl?: string;
  balance: number;
  extData?: string;
}

export interface ChannelCreateResp extends BaseMsgResp { }

export interface ChannelListReq {
  page: BaseListReq;
  name?: string;
  region?: string;
  channelType?: string;
  operator?: string;
  realNameRequired?: boolean;
}

export interface ChannelListItem {
  id: number;
  name: string;
  region: string;
  channelType: string;
  operator: string;
  contactPerson: string;
  contactPhone: string;
  balance: number;
  realNameRequired: boolean;
  realNameUrl: string;
  extData: string;
  createdAt: number;
}

export type ChannelListResp = BaseListResp<ChannelListItem>;
