import { type BaseDataResp, type BaseListResp, type BaseResp,type BaseListReq } from '../../model/baseModel';


export interface BaseListInfo {
  total: number;
}

export interface BaseMsgResp extends BaseResp {}

export interface TaskListItem {
  id: number;
  type: number;
  operatorId: string;
  status: number;
  successRecords: number;
  totalRecords: number;
  failedRecords: number;
  errorMessage: string;
  createdAt: number;
  updatedAt: number;
  extData: string;
}

export interface TaskListInfo extends BaseListInfo {
  data: TaskListItem[];
}

export type TaskListResp = BaseDataResp<TaskListInfo>;

export interface TaskListReq {
  page: BaseListReq;
  type?: number;
  status?: number;
}

export interface TaskLogInfo {
  id: number;
  taskId: number;
  recordId: number;
  recordText: string;
  status: number;
  createdAt: number;
  errorText: string;
}

export interface TaskLogListInfo extends BaseListInfo {
  data: TaskLogInfo[];
}

export type TaskLogResp = BaseDataResp<TaskLogListInfo>;

export interface TaskLogReq {
  taskId: number;
  page: BaseListReq;
  // 处理状态：1（成功）、2（失败）、0（全部）
  status?: number;
} 
