import {
  type BaseListResp,
  type BaseResp,
  type BaseListReq,
} from '../../model/baseModel';

export interface FileInfo {
  name: string;
  size: number;
  type: string;
  url: string;
}

export interface ImportDeviceReq {
  file: FileInfo;
  channel: number;
  cost: number;
  tags?: string[];
  cover: boolean;
  deviceType: string;
  model: string;
  serialPrefix?: string;
}

export type ImportDeviceResp = BaseResp;

export interface ManufacturerInfo {
  id: number;
  name: string;
  contactPerson: string;
  contactPhone: string;
  contactEmail: string;
  address: string;
  bankAccount: string;
  bankName: string;
  createdAt: number;
  updatedAt: number;
}

export interface ManufacturerCreateReq {
  id?: number;
  name: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  address?: string;
  bankAccount?: string;
  bankName?: string;
}

export type ManufacturerCreateResp = BaseResp;

export interface ManufacturerListReq {
  page: BaseListReq;
  name?: string;
  contactPerson?: string;
  contactPhone?: string;
}
export type ManufacturerListResp = BaseListResp<ManufacturerInfo>;

// 设备列表相关数据模型
export interface DeviceChannelInfo {
  // 渠道ID
  id: number;
  // 渠道名称
  name: string;
}

export interface DeviceCardInfo {
  // 卡片ID
  id: number;
  // ICCID
  iccid: string;
  // 手机号
  msisdn: string;
  // IMSI
  imsi: string;
  // 卡片状态
  status: number;
  // 激活时间
  activatedAt: number;
}

export interface DeviceInfo {
  // 设备ID
  id: number;
  // 箱号
  boxNo: string;
  // 随机码
  randomCode: string;
  // 打包时间
  packageDate: number;
  // 设备号
  deviceNo: string;
  // 设备IMEI号
  imei: string;
  // 设备IMSI号
  imsi: string;
  // 设备MAC地址
  mac: string;
  // 设备手机号
  msisdn: string;
  // 设备的CCID
  ccid: string;
  // 接入号
  accessNumber: string;
  // 设备序列号
  sn: string;
  // 设备无线网络名称
  ssid: string;
  // 设备无线网络密码
  wifiKey: string;
  // 设备2.4G无线MAC地址
  wifiMac: string;
  // 设备5G无线MAC地址
  wifiMac5g: string;
  // 记录创建时间
  createdAt: number;
  // 记录更新时间
  updatedAt: number;
  // 当前上行控速（0不限速，单位byte）
  speedUpLink: number;
  // 当前下行控速（0不限速，单位byte）
  speedDownLink: number;
  // WIFI状态（0关闭，1打开，2隐藏SSID）
  hidden: number;
  // 设备标签
  tags: string[];
  // 当前设备成本
  costPrice: number;
  // 渠道信息
  channel?: DeviceChannelInfo;
  // 关联的卡片信息
  cards?: DeviceCardInfo[];
}

export interface DeviceListReq {
  page: BaseListReq;
  // 设备ID（设备号）
  deviceNo?: string;
  // 设备IMEI
  imei?: string;
  // 卡的ICCID（通过关联卡片查询）
  iccid?: string;
  // 设备标签
  tags?: string[];
  // 所属渠道商ID
  channelId?: number;
  // 创建时间范围 - 开始时间（时间戳）
  createdAtStart?: number;
  // 创建时间范围 - 结束时间（时间戳）
  createdAtEnd?: number;
  // 打包时间范围 - 开始时间（时间戳）
  packageDateStart?: number;
  // 打包时间范围 - 结束时间（时间戳）
  packageDateEnd?: number;
}

export interface DeviceListInfo {
  total: number;
  data: DeviceInfo[];
}

export type DeviceListResp = BaseListResp<DeviceInfo>;
