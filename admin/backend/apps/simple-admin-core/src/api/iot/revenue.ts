import {
  type BaseDataResp,
  type BaseIDReq,
  type BaseListResp,
  type BaseResp,
} from '#/api/model/baseModel';
import { requestClient } from '#/api/request';

export interface RevenueListItem {
  id: number;
  costTypeId: number;
  costTypeName: string;
  relatedOrderId?: number;
  relatedEntityId?: number;
  amount: number;
  createdAt: string;
  description?: string;
  updatedAt: string;
  isRefunded: boolean;
}

export interface RevenueStatistics {
  totalAmount: number;
  todayAmount: number;
  monthAmount: number;
  transactionCount: number;
}

export interface RevenueListReq {
  page: number;
  pageSize: number;
  startTime?: string;
  endTime?: string;
}

enum Api {
  RevenueList = '/iot-api/revenue/list',
  RevenueStatistics = '/iot-api/revenue/statistics',
}

/**
 * @description: Get revenue list
 */
export const getRevenueList = (params: RevenueListReq) => {
  return requestClient.post<BaseDataResp<BaseListResp<RevenueListItem[]>>>(Api.RevenueList, params);
};

/**
 * @description: Get revenue statistics
 */
export const getRevenueStatistics = (params?: { startTime?: string; endTime?: string }) => {
  return requestClient.post<BaseDataResp<RevenueStatistics>>(Api.RevenueStatistics, params);
}; 
