import { requestClient } from '#/api/request';
import { type BaseDataResp, type BaseResp } from '../model/baseModel';
import {
  type CardListReq,
  type CardListResp,
  type ChannelCreateReq,
  type ChannelListReq,
  type ChannelListResp,
  type ImportCardReq,
} from './model/cardModel';

enum Api {
  GetCardList = '/iot-api/card/list',
  ImportCard = '/iot-api/card/import',
  CreateChannel = '/iot-api/card/channel/create',
  GetChannelList = '/iot-api/card/channel/list',
}

/**
 * @description: 获取卡列表
 */
export const getCardList = (params: CardListReq) => {
  return requestClient.post<BaseDataResp<CardListResp>>(Api.GetCardList,  params );
};

/**
 * @description: 导入卡片
 */
export const importCard = (params: ImportCardReq) => {
  return requestClient.post<BaseResp>(Api.ImportCard, params);
};

/**
 * @description: 创建通道
 */
export const createChannel = (params: ChannelCreateReq) => {
  return requestClient.post<BaseResp>(Api.CreateChannel, params);
};

/**
 * @description: 获取通道列表
 */
export const getChannelList = (params: ChannelListReq) => {
  return requestClient.post<BaseDataResp<ChannelListResp>>(Api.GetChannelList, params);
};
