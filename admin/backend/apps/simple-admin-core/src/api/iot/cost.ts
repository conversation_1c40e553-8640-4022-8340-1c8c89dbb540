import {
  type BaseDataResp,
  type BaseIDReq,
  type BaseListResp,
  type BaseResp,
} from '#/api/model/baseModel';
import { requestClient } from '#/api/request';

export interface CostType {
  id: number;
  name: string;
  description?: string;
}

export interface CostListItem {
  id: number;
  costTypeId: number;
  costTypeName: string;
  relatedOrderId?: number;
  relatedEntityId?: number;
  amount: number;
  createdAt: string;
  description?: string;
  updatedAt: string;
  isRefunded: boolean;
}

export interface CostStatistics {
  totalAmount: number;
  todayAmount: number;
  monthAmount: number;
  transactionCount: number;
}

export interface CostListReq {
  page: number;
  pageSize: number;
  startTime?: string;
  endTime?: string;
  costTypeId?: number;
}

enum Api {
  CostList = '/iot-api/cost/list',
  CostStatistics = '/iot-api/cost/statistics',
  CostTypes = '/iot-api/cost/types',
}

/**
 * @description: Get cost list
 */
export const getCostList = (params: CostListReq) => {
  return requestClient.post<BaseDataResp<BaseListResp<CostListItem[]>>>(Api.CostList, params);
};

/**
 * @description: Get cost statistics
 */
export const getCostStatistics = (params?: { startTime?: string; endTime?: string; costTypeId?: number }) => {
  return requestClient.post<BaseDataResp<CostStatistics>>(Api.CostStatistics, params);
};

/**
 * @description: Get cost types
 */
export const getCostTypes = () => {
  return requestClient.get<BaseDataResp<CostType[]>>(Api.CostTypes);
}; 
