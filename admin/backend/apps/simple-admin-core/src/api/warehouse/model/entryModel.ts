import { type BaseListResp, type BaseResp } from '../../model/baseModel';

/**
 * @description: Entry order line info
 */
export interface EntryOrderLineInfo {
  id?: number;
  itemCode: string;                    // 商品编码（设备序列号）
  itemName: string;                    // 商品名称
  planQty: number;                     // 计划入库数量
  actualQty?: number;                  // 实际入库数量
  purchasePrice?: number;              // 采购价格
  retailPrice?: number;                // 零售价格
  batchCode?: string;                  // 批次编码
  produceCode?: string;                // 生产批号
  productDate?: string;                // 生产日期
  expireDate?: string;                 // 过期日期
  boxNumber?: string;                  // 箱号
  palletNumber?: string;               // 卡板号
  snCodes?: string[];                  // SN编码列表
  shelfLocation?: string;              // 上架位置
  deviceStatus?: number;               // 设备状态
  remark?: string;                     // 备注
  createdAt?: number;
  updatedAt?: number;
}

/**
 * @description: Entry order info
 */
export interface EntryOrderInfo {
  id?: number;
  entryOrderCode?: string;             // 入库单号
  warehouseCode: string;               // 云仓编码
  orderType: string;                   // 业务类型
  purchaseOrderCode?: string;          // 采购单号
  expectStartTime?: string;            // 预期到货开始时间
  expectEndTime?: string;              // 预期到货结束时间
  actualArrivalTime?: string;          // 实际到货时间
  logisticsCode?: string;              // 物流公司编码
  logisticsName?: string;              // 物流公司名称
  expressCode?: string;                // 运单号
  supplierCode: string;                // 供应商编码
  supplierName: string;                // 供应商名称
  operatorName?: string;               // 操作员姓名
  orderStatus?: number;                // 订单状态
  remark?: string;                     // 备注
  orderLines?: EntryOrderLineInfo[];   // 入库单明细列表
  createdAt?: number;
  updatedAt?: number;
}

/**
 * @description: Create entry order line request
 */
export interface CreateEntryOrderLineReq {
  itemCode: string;
  itemName: string;
  planQty?: number;
  purchasePrice?: number;
  retailPrice?: number;
  batchCode?: string;
  produceCode?: string;
  productDate?: string;
  expireDate?: string;
  boxNumber?: string;
  palletNumber?: string;
  snCodes?: string[];
  remark?: string;
}

/**
 * @description: Create entry order request
 */
export interface CreateEntryOrderReq {
  warehouseCode: string;
  orderType?: string;
  purchaseOrderCode?: string;
  expectStartTime?: string;
  expectEndTime?: string;
  logisticsCode?: string;
  logisticsName?: string;
  expressCode?: string;
  supplierCode: string;
  supplierName: string;
  operatorName?: string;
  remark?: string;
  orderLines: CreateEntryOrderLineReq[];
}

/**
 * @description: Create entry order data
 */
export interface CreateEntryOrderData {
  id: number;
  entryOrderCode: string;
}

/**
 * @description: Create entry order response
 */
export interface CreateEntryOrderResp extends BaseResp {
  data: CreateEntryOrderData;
}

/**
 * @description: Get entry order list request
 */
export interface GetEntryOrderListReq {
  page?: number;
  pageSize?: number;
  warehouseCode?: string;
  orderStatus?: number;
  supplierCode?: string;
  startTime?: string;
  endTime?: string;
  keyword?: string;
}

/**
 * @description: Entry order list data
 */
export interface EntryOrderListData {
  list: EntryOrderInfo[];
  page: {
    page: number;
    pageSize: number;
    total: number;
  };
}

/**
 * @description: Get entry order list response
 */
export interface GetEntryOrderListResp extends BaseResp {
  data: EntryOrderListData;
}

/**
 * @description: Get entry order detail request
 */
export interface GetEntryOrderDetailReq {
  id: number;
}

/**
 * @description: Get entry order detail response
 */
export interface GetEntryOrderDetailResp extends BaseResp {
  data: EntryOrderInfo;
}

/**
 * @description: Confirm entry order line request
 */
export interface ConfirmEntryOrderLineReq {
  id: number;
  actualQty: number;
  shelfLocation?: string;
  deviceStatus?: number;
  remark?: string;
}

/**
 * @description: Confirm entry order request
 */
export interface ConfirmEntryOrderReq {
  id: number;
  actualArrivalTime?: string;
  operatorName?: string;
  remark?: string;
  orderLines?: ConfirmEntryOrderLineReq[];
}

/**
 * @description: Confirm entry order response
 */
export interface ConfirmEntryOrderResp extends BaseResp {}

/**
 * @description: Cancel entry order request
 */
export interface CancelEntryOrderReq {
  id: number;
  cancelReason: string;
  operatorName?: string;
}

/**
 * @description: Cancel entry order response
 */
export interface CancelEntryOrderResp extends BaseResp {}

/**
 * @description: Batch create entry lines request
 */
export interface BatchCreateEntryLinesReq {
  entryOrderId: number;
  orderLines: CreateEntryOrderLineReq[];
}

/**
 * @description: Batch create entry lines data
 */
export interface BatchCreateEntryLinesData {
  successCount: number;
  failCount: number;
  failDetails?: string[];
}

/**
 * @description: Batch create entry lines response
 */
export interface BatchCreateEntryLinesResp extends BaseResp {
  data: BatchCreateEntryLinesData;
}

/**
 * @description: Entry order list response
 */
export type EntryOrderListResp = BaseListResp<EntryOrderInfo>;
