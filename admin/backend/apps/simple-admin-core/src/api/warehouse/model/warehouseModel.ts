import { type BaseListResp, type BaseResp } from '../../model/baseModel';

/**
 * @description: Warehouse info
 */
export interface WarehouseInfo {
  id?: number;
  warehouseCode: string;
  warehouseName: string;
  companyName: string;
  contactPerson: string;
  contactPhone: string;
  contactEmail?: string;
  address: string;
  province: string;
  city: string;
  district?: string;
  apiEndpoint: string;
  appKey: string;
  customerId: string;
  status: number;
  createdAt?: number;
  updatedAt?: number;
}

/**
 * @description: Get warehouse list request
 */
export interface GetWarehouseListReq {
  page?: number;
  pageSize?: number;
  keyword?: string;
  status?: number;
}

/**
 * @description: Warehouse list data
 */
type WarehouseListData = BaseListResp<WarehouseInfo>;

/**
 * @description: Get warehouse list response
 */
export interface GetWarehouseListResp extends BaseResp {
  data: WarehouseListData;
}

/**
 * @description: Get warehouse detail request
 */
export interface GetWarehouseDetailReq {
  id: number;
}

/**
 * @description: Get warehouse detail response
 */
export interface GetWarehouseDetailResp extends BaseResp {
  data: WarehouseInfo;
}

/**
 * @description: Create warehouse request
 */
export interface CreateWarehouseReq {
  warehouseCode: string;
  warehouseName: string;
  companyName: string;
  contactPerson: string;
  contactPhone: string;
  contactEmail?: string;
  address: string;
  province: string;
  city: string;
  district?: string;
  apiEndpoint: string;
  appKey: string;
  appSecret: string;
  customerId: string;
}

/**
 * @description: Create warehouse data
 */
export interface CreateWarehouseData {
  id: number;
}

/**
 * @description: Create warehouse response
 */
export interface CreateWarehouseResp extends BaseResp {
  data: CreateWarehouseData;
}

/**
 * @description: Update warehouse request
 */
export interface UpdateWarehouseReq {
  id: number;
  warehouseName?: string;
  companyName?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  address?: string;
  province?: string;
  city?: string;
  district?: string;
  apiEndpoint?: string;
  appKey?: string;
  appSecret?: string;
  customerId?: string;
  status?: number;
}

/**
 * @description: Update warehouse response
 */
export interface UpdateWarehouseResp extends BaseResp {}

/**
 * @description: Warehouse list response
 */
export type WarehouseListResp = BaseListResp<WarehouseInfo>;
