import {
  type BaseDataResp,
  type BaseIDReq,
  type BaseResp,
} from '#/api/model/baseModel';
import { requestClient } from '#/api/request';

import {
  type BatchCreateEntryLinesReq,
  type BatchCreateEntryLinesResp,
  type CancelEntryOrderReq,
  type CancelEntryOrderResp,
  type ConfirmEntryOrderReq,
  type ConfirmEntryOrderResp,
  type CreateEntryOrderReq,
  type CreateEntryOrderResp,
  type GetEntryOrderDetailReq,
  type GetEntryOrderDetailResp,
  type GetEntryOrderListReq,
  type GetEntryOrderListResp,
} from './model/entryModel';

enum Api {
  CreateEntryOrder = '/warehouse/entry/order/create',
  UpdateEntryOrder = '/warehouse/entry/order/update',
  GetEntryOrderList = '/warehouse/entry/order/list',
  GetEntryOrderDetail = '/warehouse/entry/order/detail',
  ConfirmEntryOrder = '/warehouse/entry/order/confirm',
  CancelEntryOrder = '/warehouse/entry/order/cancel',
  BatchCreateEntryLines = '/warehouse/entry/lines/batch',
}

/**
 * @description: Create entry order
 */
export const createEntryOrder = (params: CreateEntryOrderReq) => {
  return requestClient.post<BaseDataResp<CreateEntryOrderResp>>(Api.CreateEntryOrder, params);
};

/**
 * @description: Update entry order
 */
export const updateEntryOrder = (params: any) => {
  return requestClient.put<BaseResp>(Api.UpdateEntryOrder, params);
};

/**
 * @description: Get entry order list
 */
export const getEntryOrderList = (params: GetEntryOrderListReq) => {
  return requestClient.get<BaseDataResp<GetEntryOrderListResp>>(Api.GetEntryOrderList, {
    params,
  });
};

/**
 * @description: Get entry order detail
 */
export const getEntryOrderDetail = (params: GetEntryOrderDetailReq) => {
  return requestClient.get<BaseDataResp<GetEntryOrderDetailResp>>(
    `${Api.GetEntryOrderDetail}/${params.id}`,
  );
};

/**
 * @description: Confirm entry order
 */
export const confirmEntryOrder = (params: ConfirmEntryOrderReq) => {
  return requestClient.put<ConfirmEntryOrderResp>(Api.ConfirmEntryOrder, params);
};

/**
 * @description: Cancel entry order
 */
export const cancelEntryOrder = (params: CancelEntryOrderReq) => {
  return requestClient.put<CancelEntryOrderResp>(Api.CancelEntryOrder, params);
};

/**
 * @description: Batch create entry lines
 */
export const batchCreateEntryLines = (params: BatchCreateEntryLinesReq) => {
  return requestClient.post<BaseDataResp<BatchCreateEntryLinesResp>>(Api.BatchCreateEntryLines, params);
};

/**
 * @description: Delete entry order
 */
export const deleteEntryOrder = (params: BaseIDReq) => {
  return requestClient.delete<BaseResp>('/warehouse/entry/order/delete', { data: params });
};

/**
 * @description: Batch delete entry orders
 */
export const batchDeleteEntryOrder = (params: { ids: number[] }) => {
  return requestClient.delete<BaseResp>('/warehouse/entry/order/delete', { data: params });
};
