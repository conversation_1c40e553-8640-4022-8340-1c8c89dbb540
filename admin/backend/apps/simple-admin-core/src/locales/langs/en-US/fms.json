{"file": {"fileName": "File Name", "fileType": "File Type", "filePath": "File Path", "fileSize": "File Size", "video": "Video", "audio": "Audio", "image": "Image", "other": "Other", "public": "Public", "private": "Private", "download": "Download", "fileList": "File List", "overSizeError": "The file is over size", "wrongTypeError": "The file type is illegal", "preprocessing": "File is preprocessing...", "copyURLFailed": "Copy the file path failed, please copy manually", "copyURLSuccess": "Copy the path successfully", "copyURL": "Copy URL", "addFile": "Add File", "editFile": "Edit File", "copyPathSuccess": "The file's URL has been successfully copied to the clipboard"}, "tag": {"tag": "Tags", "status": "Status", "name": "Name", "remark": "Remark", "addTag": "Add Tag", "editTag": "Edit Tag", "tagList": "Tag List"}, "storageProvider": {"state": "State", "name": "Name", "bucket": "Bucket", "providerName": "Provider Name", "secretId": "Secret ID", "secretKey": "Secret Key", "folder": "Folder in cloud", "region": "Region", "endpoint": "Endpoint", "isDefault": "<PERSON>", "addStorageProvider": "Add Storage Provider", "editStorageProvider": "Edit Storage Provider", "storageProviderList": "Storage Provider List", "useCdn": "Use CDN", "cdnUrl": "CDN URL", "chooseProvider": "Please select", "nameHelpMessage": "Recommend that prefix with provider type, such as \"tencent-\", \"aliyun-\"", "folderHelpMessage": "Sub folder in cloud, can be empty. Prefix with \"/\", such as \"/test\""}, "cloudFile": {"state": "State", "name": "Name", "url": "URL", "size": "Size", "fileType": "File Type", "userId": "User ID", "providerId": "Storage Provider", "addCloudFile": "Add Cloud File", "editCloudFile": "Edit Cloud File", "cloudFileList": "Cloud File List"}, "cloudFileTag": {"status": "Status", "name": "Name", "remark": "Remark", "addCloudFileTag": "Add Cloud File's Tag", "editCloudFileTag": "Edit Cloud File's Tag", "cloudFileTagList": "Cloud File's Tag List"}}