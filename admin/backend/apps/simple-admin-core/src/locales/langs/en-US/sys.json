{"api": {"operationFailed": "Operation failed", "errorTip": "<PERSON><PERSON><PERSON>", "errorMessage": "The operation failed, the system is abnormal!", "timeoutMessage": "<PERSON><PERSON> timed out, please log in again!", "apiTimeoutMessage": "The interface request timed out, please refresh the page and try again!", "apiRequestFailed": "The interface request failed, please try again later!", "networkException": "network anomaly", "networkExceptionMsg": "Please check if your network connection is normal! The network is abnormal", "errMsg401": "The user's token is expired, please sign in again", "errMsg403": "The user does not have permission to access this API, please contact administrator", "errMsg404": "Network request error, the resource was not found!", "errMsg405": "Network request error, request method not allowed!", "errMsg408": "Network request timed out!", "errMsg500": "Server error, please contact the administrator!", "errMsg501": "The network is not implemented!", "errMsg502": "Network Error!", "errMsg503": "The service is unavailable, the server is temporarily overloaded or maintained!", "errMsg504": "Network timeout!", "errMsg505": "The http version does not support the request!"}, "app": {"logoutTip": "Reminder", "logoutMessage": "Confirm to exit the system?", "menuLoading": "Menu loading..."}, "errorLog": {"tableTitle": "Error log list", "tableColumnType": "Type", "tableColumnDate": "Time", "tableColumnFile": "File", "tableColumnMsg": "Error message", "tableColumnStackMsg": "Stack info", "tableActionDesc": "Details", "modalTitle": "Error details", "fireVueError": "Fire vue error", "fireResourceError": "Fire resource error", "fireAjaxError": "Fire ajax error", "enableMessage": "Only effective when useErrorHandle=true in `/src/settings/projectSetting.ts`."}, "exception": {"backLogin": "<PERSON> Login", "backHome": "Back Home", "subTitle403": "Sorry, you don't have access to this page.", "subTitle404": "Sorry, the page you visited does not exist.", "subTitle500": "Sorry, the server is reporting an error.", "noDataTitle": "No data on the current page.", "networkErrorTitle": "Network Error", "networkErrorSubTitle": "Sorry, Your network connection has been disconnected, please check your network!"}, "lock": {"unlock": "Click to unlock", "alert": "Lock screen password error", "backToLogin": "Back to login", "entry": "Enter the system", "placeholder": "Please enter the lock screen password or user password"}, "login": {"backSignIn": "Back sign in", "mobileSignInFormTitle": "Mobile sign in", "qrSignInFormTitle": "Qr code sign in", "signInFormTitle": "Sign in", "emailSignUpFormTitle": "Sign up by email", "signUpFormTitle": "Sign up", "forgetFormTitle": "Reset password", "signInTitle": "Distributed management system", "signInDesc": "Develop microservices management system in a short time", "policy": "I agree to the xxx Privacy Policy", "scanSign": "scanning the code to complete the login", "loginButton": "Sign in", "registerButton": "Sign up", "rememberMe": "Remember me", "forgetPassword": "Forget Password?", "otherSignIn": "Sign in with", "loginSuccessTitle": "Login successful", "loginSuccessDesc": "Welcome back", "signupSuccessTitle": "Sign up successful", "signupSuccessDesc": "Welcome", "accountPlaceholder": "Please input username", "passwordPlaceholder": "Please input password", "emailPlaceholder": "Please enter your email address", "smsPlaceholder": "Please input sms code", "mobilePlaceholder": "Please input mobile", "policyPlaceholder": "Register after checking", "diffPwd": "The two passwords are inconsistent", "accountMaxLength": "Account length should be up to 30 characters", "passwordLength": "Password length should be between 6 and 30 characters", "captchaRequired": "Please enter a 5-digit captcha", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "email": "Email", "smsCode": "SMS code", "mobile": "Mobile", "captcha": "<PERSON><PERSON>", "resetPassword": "Please select a method to reset your password"}, "menu": {"menuName": "Name", "menuTitle": "Title Shown", "icon": "Icon", "order": "Order", "component": "Component", "statusName": "Status", "type": "Type", "directory": "Directory", "menu": "<PERSON><PERSON>", "button": "<PERSON><PERSON>", "menuParent": "Parent", "routePath": "Route Path", "componentPath": "Component Path", "isHttpPath": "Http Path?", "isKeepAlive": "Keep Alive?", "isShown": "Show", "isHidden": "Hidden", "isBreadcrumbShown": "Show Breadcrumb?", "currentActiveMenu": "Active Tab", "isAutoCloseTab": "Auto Close Tab?", "hideTab": "<PERSON><PERSON>", "frameSrc": "Iframe Path", "carryParam": "Carry Parameter", "hideChildrenInMenu": "Hide Children", "affix": "Affix <PERSON>b", "dynamicLevel": "Maximum opened Tab", "realPath": "Static Path", "redirectPath": "Redirect Path", "menuParamManagement": "Extra Parameters", "paramKey": "Key", "paramType": "Type", "paramValue": "Value", "paramList": "Parameter List", "rootMenu": "Root Menu", "menuList": "Menu List", "element": "Page Element", "permission": "Permission Symbol", "addMenu": "<PERSON><PERSON>", "editMenu": "<PERSON>", "addMenuParam": "Add Parameter", "editMenuParam": "Edit Parameter", "componentHelp": "The directory is \"LAYOUT\", embedded pages use \"IFrame\", and regular pages use the page path", "pathHelp": "All menus including directories must set the path", "permissionHelp": "Permission flags are used to control the display and hiding of page elements", "managementCenter": "Admin Center", "menuNameHelp": "Needs to be consistent with the component name to be cached, the default is \"Model Name\" camel case plus \"Management\", such as \"StudentManagement\"", "iconHelp": "Visit iconify.design to copy the icon", "iframeHelp": "Component should be IFrame, IFrame URL is the target URL"}, "role": {"roleList": "Role List", "roleName": "Name", "roleTitle": "Role", "roleValue": "Code", "defaultRouter": "Homepage", "admin": "Administrator", "stuff": "Stuff", "seller": "<PERSON><PERSON>", "member": "Member", "addRole": "Add Role", "editRole": "Edit Role", "adminStatusChangeForbidden": "Disallow modification of admin status", "defaultRouterHelpMessage": "The default route address to jump to after login", "roleValueHelpMessage": "The string code of the role, used for authentication"}, "apis": {"apiList": "API List", "path": "Path", "description": "Description", "method": "Method", "group": "Group", "serviceName": "Service Name", "addApi": "Add API", "editApi": "Edit API", "isRequiredHelpMessage": "Whether it is a necessary API, if it is a necessary API, it must be selected during authorization management, and cancellation is not allowed", "serviceNameHelpMessage": "The default is Other, fill in the service name such as Core, Fms"}, "authority": {"authorityManagement": "Authorization Management", "menuAuthority": "Menu Authorization", "apiAuthority": "API Authorization"}, "user": {"userList": "User List", "nickname": "Nickname", "avatar": "Avatar", "oldPassword": "Old Password", "newPassword": "New Password", "description": "Description", "homePath": "Homepage", "addUser": "Add User", "editUser": "Edit User", "changePassword": "Change Password", "forceLoggingOut": "Force logging out", "changeAvatar": "Change Avatar", "profile": "Profile Setting"}, "init": {"initTitle": "Initialize database", "initProgressTitle": "Progress", "initCoreDatabase": "Initialize core database", "initFileDatabase": "Initialize file manager database (optional)", "initMMSDatabase": "Initialize member management service database (optional)", "initJobDatabase": "Initialize scheduled task management service database (optional)", "initMcmsDatabase": "Initialize message center management service database (optional)", "initCustom": "Custom Initialization", "initUrl": "Initialization URL", "initPort": "Initialization Port", "initService": "Initialization Service", "initRedirect": "You will be redirected to <URL>:<Port>/<Service>/init/database", "initOptional": "Optional"}, "sys": {"version": "Version", "Name": "System Name", "navigation": "Navigation", "info": "System Information", "welcome": "Welcome to use Simple Admin", "changeTheme": "Change Theme", "copyToken": "<PERSON><PERSON>", "moreFeatures": "Other Operations"}, "dictionary": {"name": "Name", "dictionaryList": "Dictionary List", "dictionaryDetailList": "Key/Value List", "key": "Key", "value": "Value", "addDictionary": "Add Dictionary", "addDictionaryDetail": "Add Key/Value", "editDictionary": "Edit Dictionary", "editDictionaryDetail": "Edit Key/Value"}, "oauth": {"providerList": "Provider List", "clientId": "Client ID", "clientSecret": "Client Secret", "redirectURL": "Redirect URL", "scope": "<PERSON><PERSON>", "authURL": "Authorization URL", "tokenURL": "Token URL", "authStyle": "Authorization Style", "infoURL": "User Info Path", "autoDetect": "Auto detect", "params": "Parameters Mode", "header": "Header <PERSON>", "callback": "Callback interface", "addProvider": "Add Provider", "editProvider": "Edit Provider", "createAccount": "Please register an account with this email or bind the email to an account"}, "token": {"tokenList": "Token List"}, "department": {"status": "Status", "name": "Name", "ancestors": "Ancestors", "leader": "Leader", "phone": "Phone", "email": "Email", "sort": "Sort", "parentId": "ParentId", "addDepartment": "Add Department", "editDepartment": "Edit Department", "departmentList": "Department List", "firstLevelDepartment": "First level department", "userDepartment": "Department", "ancestorsHelpMessage": "List of parent department IDs, separated by commas. The IDs of all parent departments above the current department's parent except the current parent's ID, which is the parentId field", "departmentUndefined": "You do not belong to any department."}, "position": {"name": "Name", "code": "Code", "remark": "Remark", "addPosition": "Add Position", "editPosition": "Edit Position", "positionList": "Position List", "userPosition": "Position"}, "member": {"rankId": "Rank", "addMember": "Add Member", "editMember": "Edit Member", "memberList": "Member List", "expiredAt": "Expired Time"}, "memberRank": {"name": "Name", "description": "Description", "remark": "Remark", "addMemberRank": "Add Rank", "editMemberRank": "Edit Rank", "memberRankList": "Rank List", "code": "Code"}, "task": {"status": "Status", "name": "Task Name", "taskGroup": "Task Group", "cronExpression": "Cron Expression", "pattern": "Pattern", "payload": "Payload", "addTask": "Add Task", "editTask": "Edit Task", "taskList": "Task List"}, "taskLog": {"id": "Id", "startedAt": "Started Time", "finishedAt": "Finished Time", "result": "Result", "addTaskLog": "Add Task Log", "editTaskLog": "Edit Task Log", "taskLogList": "Task Log List"}, "configuration": {"name": "Name", "key": "Key", "value": "Value", "category": "Category", "remark": "Remark", "addConfiguration": "Add Configuration", "editConfiguration": "Edit Configuration", "configurationList": "Configuration List"}, "route": {"systemManagementTitle": "System", "menuManagementTitle": "<PERSON><PERSON>", "roleManagementTitle": "Role", "apiManagementTitle": "API", "userManagementTitle": "User", "fileManagementTitle": "File", "userProfileTitle": "Profile", "dictionaryManagementTitle": "Dictionary", "dictionaryDetailManagementTitle": "Key/Value", "oauthManagement": "<PERSON><PERSON><PERSON>", "tokenManagement": "Token", "otherPages": "Other Pages", "positionManagement": "Position", "taskManagement": "Scheduled Task"}}