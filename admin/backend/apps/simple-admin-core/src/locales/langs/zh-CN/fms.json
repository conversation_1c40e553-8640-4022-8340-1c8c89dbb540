{"file": {"fileName": "文件名称", "fileType": "文件类型", "filePath": "文件路径", "fileSize": "文件大小", "video": "视频", "audio": "音频", "image": "图片", "other": "其他", "public": "公开", "private": "私密", "download": "下载", "fileList": "文件列表", "overSizeError": "文件大小超过规定值", "wrongTypeError": "文件类型不合法", "preprocessing": "文件预处理中...", "copyURLFailed": "复制文件地址失败，请手动复制", "copyURLSuccess": "复制文件地址成功", "copyURL": "复制地址", "uploadFirst": "请先上传文件", "addFile": "添加文件", "editFile": "编辑文件", "copyPathSuccess": "已成功将文件地址复制到剪贴板"}, "tag": {"tag": "标签", "status": "状态", "name": "标签名称", "remark": "备注", "addTag": "添加标签", "editTag": "编辑标签", "tagList": "标签列表"}, "storageProvider": {"state": "状态", "name": "名称", "bucket": "存储桶", "providerName": "提供商名称", "secretId": "Secret Id", "secretKey": "Secret Key", "folder": "云服务文件夹", "region": "地区", "isDefault": "是否为默认", "addStorageProvider": "添加云存储提供商", "editStorageProvider": "编辑云存储提供商", "storageProviderList": "云存储提供商列表", "endpoint": "服务器地址", "useCdn": "是否使用 CDN", "cdnUrl": "CDN 地址", "chooseProvider": "请选择", "nameHelpMessage": "名称建议由提供商开头如 tencent-, aliyun-", "folderHelpMessage": "云服务的子文件夹, 可为空, 以 / 开头, 如 /test"}, "cloudFile": {"state": "状态", "name": "文件名", "url": "地址", "size": "文件大小", "fileType": "文件类型", "userId": "用户 ID", "providerId": "云服务提供商", "addCloudFile": "添加云文件", "editCloudFile": "编辑云文件", "cloudFileList": "云文件列表"}, "cloudFileTag": {"status": "状态", "name": "标签名称", "remark": "备注", "addCloudFileTag": "添加云文件标签", "editCloudFileTag": "编辑云文件标签", "cloudFileTagList": "云文件标签列表"}}