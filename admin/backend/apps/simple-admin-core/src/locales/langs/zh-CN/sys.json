{"api": {"operationFailed": "操作失败", "errorTip": "错误提示", "errorMessage": "操作失败,系统异常!", "timeoutMessage": "登录超时,请重新登录!", "apiTimeoutMessage": "接口请求超时,请刷新页面重试!", "apiRequestFailed": "请求出错，请稍候重试", "networkException": "网络异常", "networkExceptionMsg": "网络异常，请检查您的网络连接是否正常!", "errMsg401": "用户令牌已过期，请重新登录", "errMsg403": "用户没有权限访问此接口，请联系管理员开通", "errMsg404": "网络请求错误,未找到该资源!", "errMsg405": "网络请求错误,请求方法未允许!", "errMsg408": "网络请求超时!", "errMsg500": "服务器错误,请联系管理员!", "errMsg501": "网络未实现!", "errMsg502": "网络错误!", "errMsg503": "服务不可用，服务器暂时过载或维护!", "errMsg504": "网络超时!", "errMsg505": "http版本不支持该请求!"}, "app": {"logoutTip": "温馨提醒", "logoutMessage": "是否确认退出系统?", "menuLoading": "菜单加载中..."}, "errorLog": {"tableTitle": "错误日志列表", "tableColumnType": "类型", "tableColumnDate": "时间", "tableColumnFile": "文件", "tableColumnMsg": "错误信息", "tableColumnStackMsg": "stack信息", "tableActionDesc": "详情", "modalTitle": "错误详情", "fireVueError": "点击触发vue错误", "fireResourceError": "点击触发资源加载错误", "fireAjaxError": "点击触发ajax错误", "enableMessage": "只在`/src/settings/projectSetting.ts` 内的useErrorHandle=true时生效."}, "exception": {"backLogin": "返回登录", "backHome": "返回首页", "subTitle403": "抱歉，您无权访问此页面。", "subTitle404": "抱歉，您访问的页面不存在。", "subTitle500": "抱歉，服务器报告错误。", "noDataTitle": "当前页无数据", "networkErrorTitle": "网络错误", "networkErrorSubTitle": "抱歉，您的网络连接已断开，请检查您的网络！"}, "lock": {"unlock": "点击解锁", "alert": "锁屏密码错误", "backToLogin": "返回登录", "entry": "进入系统", "placeholder": "请输入锁屏密码或者用户密码"}, "login": {"backSignIn": "返回", "signInFormTitle": "登录", "mobileSignInFormTitle": "手机登录", "qrSignInFormTitle": "二维码登录", "emailSignUpFormTitle": "邮箱注册", "signUpFormTitle": "注册", "forgetFormTitle": "重置密码", "signInTitle": "Iot 物联管理系统", "signInDesc": "", "policy": "我同意xxx隐私政策", "scanSign": "扫码后点击\"确认\"，即可完成登录", "loginButton": "登录", "registerButton": "注册", "rememberMe": "记住我", "forgetPassword": "忘记密码?", "otherSignIn": "其他登录方式", "loginSuccessTitle": "登录成功", "loginSuccessDesc": "欢迎回来", "signupSuccessTitle": "注册成功", "signupSuccessDesc": "欢迎您的加入", "accountPlaceholder": "请输入账号", "passwordPlaceholder": "请输入密码", "emailPlaceholder": "请输入邮箱", "smsPlaceholder": "请输入验证码", "mobilePlaceholder": "请输入手机号码", "policyPlaceholder": "勾选后才能注册", "diffPwd": "两次输入密码不一致", "accountMaxLength": "账号最长为30位", "passwordLength": "密码长度为6-30位", "captchaRequired": "请输入5位验证码", "username": "用户名", "password": "密码", "confirmPassword": "确认密码", "email": "邮箱", "smsCode": "短信验证码", "mobile": "手机号码", "captcha": "验证码", "resetPassword": "请选择一种方式重置您的密码"}, "menu": {"menuName": "菜单名称", "menuTitle": "显示名称", "icon": "图标", "order": "排序", "component": "组件", "statusName": "状态", "type": "菜单类型", "directory": "目录", "menu": "菜单", "button": "按钮", "menuParent": "上级菜单", "routePath": "路由地址", "componentPath": "组件路径", "isHttpPath": "外链", "isKeepAlive": "缓存", "isShown": "显示", "isHidden": "隐藏", "isBreadcrumbShown": "显示面包屑", "currentActiveMenu": "激活窗口", "isAutoCloseTab": "自动关闭窗口", "hideTab": "隐藏Tab", "frameSrc": "Iframe地址", "carryParam": "携带参数", "hideChildrenInMenu": "隐藏全部子菜单", "affix": "固定Tab", "dynamicLevel": "能打开Tab的最大数量", "realPath": "固定的地址", "redirectPath": "跳转路径", "menuParamManagement": "额外参数", "paramKey": "参数名", "paramType": "参数类型", "paramValue": "参数值", "paramList": "参数列表", "menuList": "菜单列表", "rootMenu": "根菜单", "element": "页面元素", "permission": "权限标识", "addMenu": "新增菜单", "editMenu": "编辑菜单", "addMenuParam": "新增参数", "editMenuParam": "编辑参数", "componentHelp": "目录为 LAYOUT, 内嵌页面使用 IFrame, 普通页面则为页面路径", "pathHelp": "所有菜单包括目录都需要设置路径", "permissionHelp": "权限标识用于控制页面元素的显示隐藏", "managementCenter": "管理中心", "menuNameHelp": "需要与组件名一致才能缓存，默认为 Model Name 驼峰加 Management, 如 StudentManagement", "iconHelp": "访问 iconify.design 复制 icon", "iframeHelp": "组件需设置为IFrame, IFrame地址为网址"}, "role": {"roleList": "角色列表", "roleName": "名称", "roleValue": "角色代码", "roleTitle": "角色", "defaultRouter": "默认路由", "admin": "管理员", "stuff": "员工", "seller": "销售", "member": "会员", "addRole": "新增角色", "editRole": "编辑角色", "adminStatusChangeForbidden": "禁止修改管理员状态", "defaultRouterHelpMessage": "登录后默认跳转的路由地址", "roleValueHelpMessage": "角色的字符串代码，用于鉴权"}, "apis": {"apiList": "接口列表", "path": "路径", "description": "描述", "method": "方法", "group": "分组", "serviceName": "所属服务", "addApi": "添加接口", "editApi": "编辑接口", "isRequiredHelpMessage": "是否为必要接口,如果是必要接口,则在权限管理时为必选,不允许取消", "serviceNameHelpMessage": "默认是 Other, 填入服务名称如 Core, Fms"}, "authority": {"authorityManagement": "权限管理", "menuAuthority": "菜单权限", "apiAuthority": "接口权限"}, "user": {"userList": "用户列表", "nickname": "昵称", "avatar": "头像", "oldPassword": "旧密码", "newPassword": "新密码", "description": "用户描述", "homePath": "用户首页", "addUser": "新增用户", "editUser": "编辑用户", "changePassword": "修改密码", "forceLoggingOut": "强制登出", "changeAvatar": "更换头像", "profile": "个人信息设置"}, "init": {"initTitle": "数据库初始化", "initProgressTitle": "处理进度", "initCoreDatabase": "核心数据库初始化", "initFileDatabase": "文件管理数据库初始化（可选）", "initMMSDatabase": "会员服务(MMS)数据库初始化（可选）", "initJobDatabase": "定时任务服务数据库初始化（可选）", "initMcmsDatabase": "消息中心服务数据库初始化（可选）", "initCustom": "自助初始化", "initUrl": "初始化域名或 IP 地址", "initPort": "初始化端口", "initService": "初始化服务名", "initRedirect": "您将跳转到 <URL>:<Port>/<Service>/init/database", "initOptional": "可选"}, "sys": {"version": "系统版本", "Name": "系统名称", "navigation": "快捷导航", "info": "系统信息", "welcome": "欢迎使用 Simple Admin", "changeTheme": "切换主题", "copyToken": "复制令牌", "moreFeatures": "其他功能"}, "dictionary": {"name": "名称", "dictionaryList": "字典列表", "dictionaryDetailList": "键值列表", "key": "键", "value": "值", "addDictionary": "添加字典", "addDictionaryDetail": "添加键值", "editDictionary": "编辑字典", "editDictionaryDetail": "编辑键值"}, "oauth": {"providerList": "第三方列表", "clientId": "客户端ID", "clientSecret": "客户端密码", "redirectURL": "重定向地址", "scope": "权限范围", "authURL": "鉴权地址", "tokenURL": "获取Token的地址", "authStyle": "鉴权方式", "infoURL": "获取个人信息地址", "autoDetect": "自动检测", "params": "参数模式", "header": "header模式", "callback": "回调接口", "addProvider": "添加第三方", "editProvider": "编辑第三方", "createAccount": "请创建一个该邮箱的账号或绑定该邮箱到一个账号"}, "token": {"tokenList": "Token列表"}, "department": {"status": "状态", "name": "名称", "ancestors": "父级列表", "leader": "部门负责人", "phone": "电话号码", "email": "电子邮箱", "sort": "排序", "parentId": "父级ID", "addDepartment": "添加部门", "editDepartment": "编辑部门", "departmentList": "部门列表", "firstLevelDepartment": "一级部门", "userDepartment": "所属部门", "ancestorsHelpMessage": "父级部门ID列表, 用逗号分隔, 除了当前部门的父级之上的所有父级部门的 ID, 当前父级的 ID 是 parentId 字段", "departmentUndefined": "您尚未属于任何部门"}, "position": {"name": "名称", "code": "职位代码", "addPosition": "添加职位", "editPosition": "编辑职位", "positionList": "职位列表", "userPosition": "职位"}, "member": {"rankId": "会员等级", "addMember": "添加会员", "editMember": "编辑会员", "memberList": "会员列表", "expiredAt": "到期时间"}, "memberRank": {"name": "名称", "description": "描述", "remark": "备注", "addMemberRank": "添加会员等级", "editMemberRank": "编辑会员等级", "memberRankList": "会员等级列表", "code": "等级代码"}, "task": {"status": "任务状态", "name": "任务名称", "taskGroup": "任务分组", "cronExpression": "Cron表达式", "pattern": "任务标识", "payload": "任务数据", "addTask": "添加任务", "editTask": "编辑任务", "taskList": "任务列表"}, "taskLog": {"id": "Id", "startedAt": "开始时间", "finishedAt": "完成时间", "result": "任务结果", "addTaskLog": "添加任务日志", "editTaskLog": "编辑任务日志", "taskLogList": "任务日志列表"}, "configuration": {"name": "名称", "key": "键名", "value": "值", "category": "分类", "remark": "备注", "addConfiguration": "添加参数配置", "editConfiguration": "编辑参数配置", "configurationList": "参数配置列表"}, "route": {"systemManagementTitle": "系统管理", "menuManagementTitle": "菜单管理", "roleManagementTitle": "角色管理", "apiManagementTitle": "API管理", "userManagementTitle": "用户管理", "fileManagementTitle": "文件管理", "userProfileTitle": "用户个人信息", "dictionaryManagementTitle": "字典管理", "dictionaryDetailManagementTitle": "键值管理", "oauthManagement": "<PERSON><PERSON><PERSON>管理", "tokenManagement": "Token管理", "otherPages": "其他页面", "positionManagement": "职位管理", "taskManagement": "定时任务管理"}}